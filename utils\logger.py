"""
超级简单的日志处理器 - 解决显示问题
"""
import os
import logging
from datetime import datetime
from typing import Optional, Callable


class UltraSimpleGUILogHandler:
    """超级简单的GUI日志处理器"""
    
    def __init__(self, max_lines: int = 50):
        self.text_widget = None
        self.max_lines = max_lines
        self.current_lines = 0
        
    def set_text_widget(self, text_widget):
        """设置文本控件"""
        self.text_widget = text_widget
        if self.text_widget:
            # 最基础设置
            self.text_widget.setAcceptRichText(False)
            from PyQt5.QtWidgets import QTextEdit
            self.text_widget.setLineWrapMode(QTextEdit.NoWrap)
            
            # 设置字体
            from PyQt5.QtGui import QFont
            font = QFont("Courier New", 9)
            self.text_widget.setFont(font)
    
    def append_log(self, message: str, level: str = "INFO"):
        """添加日志 - 超级简单版本"""
        if not self.text_widget:
            return
            
        try:
            # 基础处理
            if isinstance(message, bytes):
                message = message.decode('utf-8', errors='ignore')
            
            # 移除特殊字符
            message = ''.join(char for char in message if ord(char) >= 32 or char in '\n\t')
            message = message.strip()
            
            # 限制长度
            if len(message) > 100:
                message = message[:97] + "..."
            
            # 时间戳
            timestamp = datetime.now().strftime("[%H:%M:%S]")
            log_line = f"{timestamp} {message}"
            
            # 行数控制
            self.current_lines += 1
            if self.current_lines > self.max_lines:
                self.text_widget.clear()
                self.current_lines = 1
            
            # 获取当前文本
            current_text = self.text_widget.toPlainText()
            
            # 添加新行
            if current_text:
                new_text = current_text + "\n" + log_line
            else:
                new_text = log_line
            
            # 设置文本
            self.text_widget.setPlainText(new_text)
            
            # 滚动到底部
            from PyQt5.QtGui import QTextCursor
            cursor = self.text_widget.textCursor()
            cursor.movePosition(QTextCursor.End)
            self.text_widget.setTextCursor(cursor)
            
        except Exception as e:
            print(f"日志错误: {e}")
    
    def clear_logs(self):
        """清空日志"""
        try:
            if self.text_widget:
                self.text_widget.clear()
                self.current_lines = 0
        except:
            pass
    
    def get_log_count(self):
        """获取行数"""
        return self.current_lines


class UltraSimpleLogger:
    """超级简单的日志记录器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.gui_callback = None
        
        # 创建日志目录
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
    
    def set_gui_callback(self, callback: Callable):
        """设置GUI回调"""
        self.gui_callback = callback
    
    def _log(self, message: str, level: str = "INFO"):
        """记录日志"""
        try:
            # GUI显示
            if self.gui_callback:
                self.gui_callback(message, level)
            
            # 控制台输出
            print(f"[{level}] {message}")
            
        except Exception as e:
            print(f"日志失败: {e}")
    
    def info(self, message: str):
        self._log(message, "INFO")
    
    def success(self, message: str):
        self._log(message, "SUCCESS")
    
    def warning(self, message: str):
        self._log(message, "WARNING")
    
    def error(self, message: str):
        self._log(message, "ERROR")
    
    def debug(self, message: str):
        self._log(message, "DEBUG")
    
    def log(self, message: str, level: str = "INFO"):
        self._log(message, level)


# 全局实例
_global_logger = None
_global_gui_handler = None

def get_logger() -> UltraSimpleLogger:
    """获取日志实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = UltraSimpleLogger()
    return _global_logger

def get_gui_handler() -> UltraSimpleGUILogHandler:
    """获取GUI处理器"""
    global _global_gui_handler
    if _global_gui_handler is None:
        _global_gui_handler = UltraSimpleGUILogHandler()
    return _global_gui_handler

def setup_logger(log_dir: str = "logs") -> UltraSimpleLogger:
    """设置日志"""
    global _global_logger
    _global_logger = UltraSimpleLogger(log_dir)
    return _global_logger

# 兼容性别名
SimpleGUILogHandler = UltraSimpleGUILogHandler
SimpleLogger = UltraSimpleLogger
