#!/usr/bin/env python3
"""
测试定时运行功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTime
from config.settings import get_settings
from gui.config_dialog import ConfigDialog

def test_scheduler_config():
    """测试定时运行配置"""
    print("=" * 50)
    print("测试定时运行配置功能")
    print("=" * 50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 获取设置
    settings = get_settings()
    
    # 显示当前配置
    print(f"当前定时运行状态: {settings.get('app.scheduled_run_enabled', False)}")
    print(f"当前定时运行时间: {settings.get('app.scheduled_run_time', '06:00')}")
    
    # 创建配置对话框
    dialog = ConfigDialog()
    
    # 测试时间解析
    time_str = "08:30"
    time_obj = QTime.fromString(time_str, 'HH:mm')
    print(f"时间解析测试: {time_str} -> {time_obj.toString('HH:mm')} (有效: {time_obj.isValid()})")
    
    # 显示对话框
    print("\n打开配置对话框，请测试定时运行设置...")
    result = dialog.exec_()
    
    if result:
        print("\n配置已保存")
        print(f"新的定时运行状态: {settings.get('app.scheduled_run_enabled', False)}")
        print(f"新的定时运行时间: {settings.get('app.scheduled_run_time', '06:00')}")
    else:
        print("\n配置已取消")
    
    print("测试完成")

if __name__ == "__main__":
    test_scheduler_config()
