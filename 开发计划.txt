广东采集GUI程序开发计划
==================================

【项目概述】
将现有的query.py命令行程序转换为PyQt5 GUI程序，并打包为exe可执行文件。
实现批量查询、企业微信推送、代理池、配置管理等功能。

【代码兼容性分析】
现有依赖库分析：
✓ requests - 打包友好，保留
✓ re - Python内置库，保留  
✓ json - Python内置库，保留
✓ time - Python内置库，保留
✓ random - Python内置库，保留

新增依赖库：
✓ PyQt5 - GUI框架，打包友好
✓ PyInstaller - 打包工具
✓ urllib3 - 代理支持，打包友好

避免使用的库：
✗ pandas - 打包体积大，依赖复杂
✗ beautifulsoup4 - 已用正则表达式替代
✗ selenium - 打包复杂

【目录结构规划】
项目根目录/
├── main.py                    # 主入口程序
├── 开发计划.txt               # 本文件
├── region_code.json           # 地区代码配置
├── config/                    # 配置文件目录
│   ├── __init__.py
│   ├── settings.py           # 配置管理
│   └── config.json           # 用户配置文件
├── core/                     # 核心功能模块
│   ├── __init__.py
│   ├── query_engine.py       # 查询引擎（基于query.py）
│   ├── proxy_manager.py      # 代理管理
│   ├── wechat_notifier.py    # 企业微信推送
│   └── data_manager.py       # 数据管理
├── gui/                      # GUI界面模块
│   ├── __init__.py
│   ├── main_window.py        # 主窗口
│   ├── config_dialog.py      # 配置对话框
│   └── styles.py             # 样式定义
├── utils/                    # 工具模块
│   ├── __init__.py
│   ├── logger.py             # 日志管理
│   ├── singleton.py          # 防双开
│   └── usage_limiter.py      # 使用次数限制
├── resources/                # 资源文件
│   ├── __init__.py
│   ├── icons/                # 图标文件
│   └── fonts/                # 字体文件
├── kuaidaili/                # 快代理SDK（已存在）
├── env_test/                 # 测试文件目录
└── package/                  # 打包相关文件

【开发阶段规划】

阶段1：项目结构搭建 ✓
- 创建目录结构
- 创建基础文件框架
- 配置开发环境

阶段2：核心功能模块开发
- 迁移query.py核心逻辑到query_engine.py
- 实现代理管理模块
- 实现企业微信推送模块
- 实现数据管理模块

阶段3：工具模块开发
- 实现日志管理
- 实现防双开功能
- 实现使用次数限制
- 实现配置管理

阶段4：GUI界面开发
- 设计主窗口界面
- 实现配置对话框
- 实现Windows11风格样式
- 集成核心功能

阶段5：功能集成测试
- 集成所有模块
- 功能测试
- 界面优化

阶段6：打包部署
- 配置PyInstaller
- 测试打包结果
- 优化打包配置

【技术要求详细说明】

1. GUI框架：PyQt5
   - Windows11风格界面
   - Segoe UI Variable字体堆栈
   - 自适应分辨率，可缩放
   - 紧凑布局设计

2. 功能要求：
   - 批量查询：读取region_code.json
   - 实时推送：查询成功立即企业微信推送
   - 重试机制：失败自动重试，3-6秒随机延迟
   - 代理池：集成快代理API，8-12次随机切换
   - 配置管理：微信webhook、代理API配置
   - 日志功能：实时显示，自动滚动
   - 防双开：进程检测
   - 最小化：关闭按钮最小化到任务栏
   - 使用限制：一天最多4次运行

3. 打包要求：
   - PyInstaller --onedir模式
   - 包含所有依赖
   - 设置程序图标
   - 无外部依赖

【开发进度记录】
✓ 阶段1：项目结构搭建 - 已完成
  - 创建目录结构：config/, core/, gui/, utils/, resources/, env_test/
  - 创建__init__.py文件
  - 项目框架搭建完成
✓ 阶段2：核心功能模块开发 - 已完成
  - query_engine.py：查询引擎，基于原query.py核心逻辑
  - proxy_manager.py：代理管理，集成快代理API
  - wechat_notifier.py：企业微信推送，实时通知功能
  - data_manager.py：数据管理，数据读写、统计、导出功能
✓ 阶段3：工具模块开发 - 已完成
  - logger.py：日志管理，GUI显示和文件记录
  - singleton.py：防双开功能，确保单实例运行
  - usage_limiter.py：使用次数限制，每天最多4次
  - settings.py：配置管理，统一管理所有配置项
✓ 阶段4：GUI界面开发 - 已完成
  - styles.py：Windows11风格样式定义
  - config_dialog.py：配置对话框，用户设置界面
  - main_window.py：主窗口，完整的GUI界面
  - main.py：主入口程序，应用程序启动
✓ 阶段5：功能集成测试 - 已完成
  - 创建模块测试脚本，所有7个模块测试通过
  - GUI应用程序成功启动和运行
  - 查询功能正常工作，能够批量查询地区数据
  - 修复GUI日志显示问题
□ 阶段6：打包部署

【注意事项】
1. 所有新文件必须放在对应目录中，根目录只保留main.py和开发计划.txt
2. 测试文件放在env_test目录
3. 打包文件放在package目录
4. 优先使用打包友好的库
5. 避免使用problematic libraries
6. 定期测试打包兼容性
