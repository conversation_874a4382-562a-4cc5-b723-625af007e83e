@echo off
echo ============================================================
echo 登记设立批量查询 - 打包脚本
echo ============================================================

:: 设置编码为UTF-8
chcp 65001 > nul

:: 检查Python环境
python --version > nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境！
    pause
    exit /b 1
)

:: 检查PyInstaller
python -c "import PyInstaller" > nul 2>&1
if errorlevel 1 (
    echo 安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo 错误：PyInstaller安装失败！
        pause
        exit /b 1
    )
)

:: 检查依赖
echo 检查依赖包...
python -c "import PyQt5" > nul 2>&1
if errorlevel 1 (
    echo 安装PyQt5...
    pip install PyQt5
)

python -c "import requests" > nul 2>&1
if errorlevel 1 (
    echo 安装requests...
    pip install requests
)

python -c "import psutil" > nul 2>&1
if errorlevel 1 (
    echo 安装psutil...
    pip install psutil
)

:: 切换到项目根目录
cd /d "%~dp0\.."

:: 清理之前的构建
echo 清理之前的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"

:: 开始打包
echo 开始打包...
pyinstaller --onedir ^
    --windowed ^
    --name="登记设立批量查询" ^
    --icon="resources/icons/学校查询.svg" ^
    --distpath="package/dist" ^
    --workpath="package/build" ^
    --specpath="package" ^
    --add-data="region_code.json;." ^
    --hidden-import="PyQt5.QtCore" ^
    --hidden-import="PyQt5.QtGui" ^
    --hidden-import="PyQt5.QtWidgets" ^
    --hidden-import="requests" ^
    --hidden-import="psutil" ^
    --exclude-module="tkinter" ^
    --exclude-module="matplotlib" ^
    --exclude-module="numpy" ^
    --exclude-module="pandas" ^
    --noconfirm ^
    main.py

if errorlevel 1 (
    echo 错误：打包失败！
    pause
    exit /b 1
)

:: 复制必要文件到输出目录
echo 复制必要文件...
copy "region_code.json" "package\dist\登记设立批量查询\" > nul
if exist "README.md" copy "README.md" "package\dist\登记设立批量查询\" > nul

:: 创建启动脚本
echo 创建启动脚本...
echo @echo off > "package\dist\登记设立批量查询\启动.bat"
echo cd /d "%%~dp0" >> "package\dist\登记设立批量查询\启动.bat"
echo "登记设立批量查询.exe" >> "package\dist\登记设立批量查询\启动.bat"

:: 打包完成
echo ============================================================
echo 打包完成！
echo 输出目录: package\dist\登记设立批量查询\
echo ============================================================

:: 询问是否打开输出目录
set /p choice="是否打开输出目录？(y/n): "
if /i "%choice%"=="y" (
    explorer "package\dist\登记设立批量查询\"
)

pause
