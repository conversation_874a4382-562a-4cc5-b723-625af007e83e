"""
企业微信推送模块
实现查询成功后立即推送通知功能
"""
import requests
import json
import time
from typing import List, Dict, Optional
from datetime import datetime


class WeChatNotifier:
    """企业微信通知器"""
    
    def __init__(self, webhook_url: str = "", logger=None):
        self.webhook_url = webhook_url
        self.logger = logger
        self.sent_count = 0
        self.last_send_time = 0
        self.rate_limit_delay = 1  # 发送间隔限制（秒）
        
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        if self.logger:
            self.logger.log(message, level)
        else:
            print(f"[{level}] {message}")
    
    def set_webhook_url(self, webhook_url: str):
        """设置webhook URL"""
        self.webhook_url = webhook_url
        self.log(f"企业微信webhook已更新")
    
    def format_school_data(self, school_data: List[Dict], region_name: str) -> str:
        """格式化学校数据为消息文本"""
        if not school_data:
            return f"📍 {region_name} - 未发现新的学校记录"

        message_lines = []

        # 限制显示的记录数量，避免消息过长
        max_display = min(3, len(school_data))
        for i, item in enumerate(school_data[:max_display], 1):
            # 使用新的模板格式
            message_lines.append("🏫 发现新的学校记录！")
            message_lines.append(f"名称：{item.get('单位名称', 'N/A')}")
            message_lines.append(f"申办事项：{item.get('申办事项', 'N/A')}")
            message_lines.append(f"信用代码：{item.get('统一社会信用代码', 'N/A')}")
            message_lines.append(f"通知时间：{item.get('通知时间', 'N/A')}")

            if i < max_display:  # 不是最后一条记录时添加分隔线
                message_lines.append("─" * 30)

        if len(school_data) > max_display:
            message_lines.append("")
            message_lines.append(f"📊 还有 {len(school_data) - max_display} 条记录未显示")
            message_lines.append(f"📍 地区：{region_name}")

        return "\n".join(message_lines)
    
    def create_message_payload(self, content: str, msg_type: str = "text") -> Dict:
        """创建消息载荷"""
        if msg_type == "text":
            return {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }
        elif msg_type == "markdown":
            return {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }
        else:
            raise ValueError(f"不支持的消息类型: {msg_type}")
    
    def send_notification(self, school_data: List[Dict], region_name: str, 
                         msg_type: str = "text") -> bool:
        """发送通知"""
        if not self.webhook_url:
            self.log("企业微信webhook未配置，跳过推送", "WARNING")
            return False
        
        # 频率限制
        current_time = time.time()
        if current_time - self.last_send_time < self.rate_limit_delay:
            time.sleep(self.rate_limit_delay - (current_time - self.last_send_time))
        
        try:
            # 格式化消息内容
            content = self.format_school_data(school_data, region_name)
            
            # 创建消息载荷
            payload = self.create_message_payload(content, msg_type)
            
            # 发送请求 - 使用更短的超时时间，避免阻塞
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=5  # 减少到5秒，避免长时间阻塞
            )
            
            self.last_send_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.sent_count += 1
                    self.log(f"企业微信推送成功 - {region_name} ({len(school_data)}条记录)")
                    return True
                else:
                    self.log(f"企业微信推送失败: {result.get('errmsg', '未知错误')}", "ERROR")
            else:
                self.log(f"企业微信推送失败，HTTP状态码: {response.status_code}", "ERROR")
                
        except requests.exceptions.Timeout:
            self.log("企业微信推送超时", "ERROR")
        except requests.exceptions.RequestException as e:
            self.log(f"企业微信推送网络错误: {e}", "ERROR")
        except Exception as e:
            self.log(f"企业微信推送异常: {e}", "ERROR")
        
        return False
    
    def send_summary_notification(self, summary_data: Dict) -> bool:
        """发送汇总通知"""
        if not self.webhook_url:
            return False
        
        try:
            content = f"""📊 批量查询完成汇总报告

🎯 查询结果：
• 总地区数：{summary_data.get('total_regions', 0)}
• 成功地区数：{summary_data.get('success_regions', 0)}
• 新增记录：{summary_data.get('new_records', 0)} 条
• 总记录数：{summary_data.get('total_records', 0)} 条

⏰ 完成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📤 本次推送：{self.sent_count} 次"""
            
            payload = self.create_message_payload(content)
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.log("汇总报告推送成功")
                    return True
                else:
                    self.log(f"汇总报告推送失败: {result.get('errmsg', '未知错误')}", "ERROR")
            
        except Exception as e:
            self.log(f"汇总报告推送异常: {e}", "ERROR")
        
        return False
    
    def send_error_notification(self, error_message: str, region_name: str = "") -> bool:
        """发送错误通知"""
        if not self.webhook_url:
            return False
        
        try:
            content = f"""⚠️ 查询异常通知

📍 地区：{region_name if region_name else '未知'}
❌ 错误：{error_message}
⏰ 时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            payload = self.create_message_payload(content)
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('errcode') == 0
                
        except Exception as e:
            self.log(f"错误通知推送异常: {e}", "ERROR")
        
        return False
    
    def test_webhook(self) -> bool:
        """测试webhook连接"""
        if not self.webhook_url:
            self.log("webhook URL未配置", "ERROR")
            return False
        
        try:
            test_content = f"🔧 企业微信推送测试\n⏰ 测试时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            payload = self.create_message_payload(test_content)
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.log("企业微信webhook测试成功")
                    return True
                else:
                    self.log(f"企业微信webhook测试失败: {result.get('errmsg', '未知错误')}", "ERROR")
            else:
                self.log(f"企业微信webhook测试失败，HTTP状态码: {response.status_code}", "ERROR")
                
        except Exception as e:
            self.log(f"企业微信webhook测试异常: {e}", "ERROR")
        
        return False
    
    def get_stats(self) -> Dict:
        """获取推送统计信息"""
        return {
            "webhook_configured": bool(self.webhook_url),
            "sent_count": self.sent_count,
            "last_send_time": self.last_send_time,
            "rate_limit_delay": self.rate_limit_delay
        }
