#!/usr/bin/env python3
"""
测试延迟时间修改
"""
import sys
import os
import random
import time
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_delay_timing():
    """测试延迟时间"""
    print("=" * 50)
    print("测试新的延迟时间设置（3-6秒）")
    print("=" * 50)
    
    # 模拟5次延迟测试
    for i in range(5):
        print(f"\n第 {i+1} 次测试:")
        
        # 使用与代码中相同的延迟逻辑
        delay_time = random.uniform(3, 6)
        print(f"等待 {delay_time:.1f} 秒...")
        
        start_time = time.time()
        time.sleep(delay_time)
        actual_time = time.time() - start_time
        
        print(f"实际延迟: {actual_time:.1f} 秒")
        
        # 验证延迟时间在3-6秒范围内
        if 3.0 <= actual_time <= 6.5:  # 允许一点误差
            print("✅ 延迟时间正确")
        else:
            print("❌ 延迟时间异常")

def test_config_removal():
    """测试配置项删除"""
    print("\n" + "=" * 50)
    print("测试配置项删除")
    print("=" * 50)
    
    from config.settings import get_settings
    settings = get_settings()
    
    # 检查已删除的配置项
    removed_configs = [
        'query.query_delay_min',
        'query.query_delay_max'
    ]
    
    for config_key in removed_configs:
        value = settings.get(config_key, None)
        if value is None:
            print(f"✅ {config_key}: 已成功删除")
        else:
            print(f"❌ {config_key}: 仍然存在，值为 {value}")
    
    # 检查保留的配置项
    remaining_configs = [
        ('query.max_retries', 3),
        ('query.retry_delay_min', 3),
        ('query.retry_delay_max', 6),
        ('query.timeout', 10)
    ]
    
    print("\n保留的配置项:")
    for config_key, expected in remaining_configs:
        value = settings.get(config_key, None)
        if value == expected:
            print(f"✅ {config_key}: {value}")
        else:
            print(f"❌ {config_key}: 期望 {expected}，实际 {value}")

if __name__ == "__main__":
    test_delay_timing()
    test_config_removal()
    print("\n测试完成！")
