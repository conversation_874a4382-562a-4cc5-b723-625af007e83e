#!/usr/bin/env python3
"""
创建程序图标
"""
import os
from PIL import Image, ImageDraw, ImageFont

def create_school_icon():
    """创建学校查询图标"""
    # 创建256x256的图标
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 背景圆形
    margin = 20
    circle_size = size - 2 * margin
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                 fill=(0, 120, 212, 255), outline=(0, 90, 180, 255), width=3)
    
    # 绘制学校建筑轮廓
    building_color = (255, 255, 255, 255)
    
    # 主建筑
    building_width = 120
    building_height = 80
    building_x = (size - building_width) // 2
    building_y = size // 2 - 10
    
    draw.rectangle([building_x, building_y, building_x + building_width, building_y + building_height],
                   fill=building_color, outline=(200, 200, 200, 255), width=2)
    
    # 屋顶
    roof_points = [
        (building_x - 10, building_y),
        (building_x + building_width // 2, building_y - 30),
        (building_x + building_width + 10, building_y)
    ]
    draw.polygon(roof_points, fill=building_color, outline=(200, 200, 200, 255))
    
    # 窗户
    window_size = 15
    window_spacing = 25
    for i in range(3):
        for j in range(2):
            window_x = building_x + 20 + i * window_spacing
            window_y = building_y + 15 + j * 25
            draw.rectangle([window_x, window_y, window_x + window_size, window_y + window_size],
                          fill=(0, 120, 212, 255))
    
    # 门
    door_width = 20
    door_height = 35
    door_x = building_x + (building_width - door_width) // 2
    door_y = building_y + building_height - door_height
    draw.rectangle([door_x, door_y, door_x + door_width, door_y + door_height],
                   fill=(139, 69, 19, 255))
    
    # 添加文字 "学"
    try:
        # 尝试使用系统字体
        font_size = 40
        try:
            font = ImageFont.truetype("msyh.ttc", font_size)  # 微软雅黑
        except:
            try:
                font = ImageFont.truetype("simhei.ttf", font_size)  # 黑体
            except:
                font = ImageFont.load_default()
        
        text = "学"
        # 获取文字边界框
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        text_x = (size - text_width) // 2
        text_y = building_y + building_height + 20
        
        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    except:
        # 如果字体加载失败，绘制一个简单的符号
        symbol_size = 30
        symbol_x = (size - symbol_size) // 2
        symbol_y = building_y + building_height + 20
        draw.ellipse([symbol_x, symbol_y, symbol_x + symbol_size, symbol_y + symbol_size],
                     fill=(255, 255, 255, 255))
    
    return img

def main():
    """主函数"""
    print("🎨 创建程序图标...")
    
    try:
        # 创建图标
        icon_img = create_school_icon()
        
        # 保存为不同尺寸的ICO文件
        icon_sizes = [(256, 256), (128, 128), (64, 64), (48, 48), (32, 32), (16, 16)]
        
        # 创建多尺寸图标列表
        icon_images = []
        for size in icon_sizes:
            resized_img = icon_img.resize(size, Image.Resampling.LANCZOS)
            icon_images.append(resized_img)
        
        # 保存ICO文件
        output_path = '../resources/icons/app.ico'  # 使用英文文件名
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 保存为ICO格式，使用标准方法
        icon_img.save(output_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (48, 48), (32, 32), (16, 16)])

        # 也保存一个中文名的副本
        output_path_cn = '../resources/icons/学校查询.ico'
        icon_img.save(output_path_cn, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (48, 48), (32, 32), (16, 16)])
        
        print(f"✅ 英文图标已创建: {output_path}")
        print(f"✅ 中文图标已创建: {output_path_cn}")

        # 也保存一个PNG版本用于预览
        png_path = '../resources/icons/app.png'
        icon_img.save(png_path, format='PNG')
        print(f"✅ PNG预览已创建: {png_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        # 安装Pillow如果没有的话
        try:
            from PIL import Image, ImageDraw, ImageFont
        except ImportError:
            print("📦 安装Pillow...")
            import subprocess
            subprocess.run(['pip', 'install', 'Pillow'], check=True)
            from PIL import Image, ImageDraw, ImageFont
        
        success = main()
        if success:
            print("\n🎉 图标创建完成！")
        else:
            print("\n❌ 图标创建失败！")
            
    except Exception as e:
        print(f"❌ 程序出错: {e}")
        input("按回车键退出...")
