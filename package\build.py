#!/usr/bin/env python3
"""
程序打包脚本
"""
import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除 {dir_name}")
    
    # 清理.pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

def check_requirements():
    """检查依赖"""
    print("📦 检查依赖...")
    required_packages = [
        'PyQt5',
        'requests',
        'pyinstaller'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'PyQt5':
                import PyQt5.QtCore
            elif package == 'pyinstaller':
                import PyInstaller
            else:
                __import__(package.lower().replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package}")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def build_exe():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")

    # 获取当前工作目录（应该是项目根目录）
    current_dir = os.getcwd()
    main_py_path = os.path.join(current_dir, 'main.py')

    # 检查main.py是否存在
    if not os.path.exists(main_py_path):
        print(f"   ❌ 找不到主程序文件: {main_py_path}")
        print(f"   当前工作目录: {current_dir}")
        return False

    print(f"   ✅ 找到主程序文件: {main_py_path}")

    # PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',                    # 单文件模式
        '--windowed',                   # 无控制台窗口
        '--name=登记设立批量查询',        # 程序名称
        '--icon=resources/icons/app.ico',  # 图标（如果存在）
        # 添加资源文件
        '--add-data=resources;resources',  # 包含整个resources目录
        '--hidden-import=PyQt5.QtCore',
        '--hidden-import=PyQt5.QtGui',
        '--hidden-import=PyQt5.QtWidgets',
        '--hidden-import=requests',
        '--hidden-import=json',
        '--hidden-import=configparser',
        '--hidden-import=utils',
        '--hidden-import=utils.singleton',
        '--hidden-import=utils.logger',
        '--hidden-import=config',
        '--hidden-import=config.settings',
        '--hidden-import=gui',
        '--hidden-import=gui.main_window',
        '--hidden-import=gui.styles',
        '--hidden-import=gui.config_dialog',
        '--hidden-import=core',
        '--hidden-import=core.query_engine',
        '--hidden-import=core.proxy_manager',
        '--hidden-import=core.data_manager',
        '--hidden-import=core.region_data',
        '--hidden-import=core.wechat_notifier',
        '--hidden-import=kuaidaili',
        '--hidden-import=kuaidaili.client',
        '--hidden-import=kuaidaili.config',
        '--clean',                      # 清理临时文件
        main_py_path                    # 使用绝对路径的主程序文件
    ]

    # 检查图标文件是否存在（优先使用英文文件名）
    icon_paths = [
        'resources/icons/app.ico',
        'resources/icons/学校查询.ico',
        'logo/app.ico'
    ]

    icon_found = False
    for icon_path in icon_paths:
        full_icon_path = os.path.join(current_dir, icon_path)
        print(f"   🔍 检查图标文件: {full_icon_path}")
        if os.path.exists(full_icon_path):
            # 更新命令中的图标路径
            for i, arg in enumerate(cmd):
                if arg.startswith('--icon='):
                    cmd[i] = f'--icon={full_icon_path}'
                    icon_found = True
                    print(f"   ✅ 找到图标文件: {full_icon_path}")
                    break
            break
        else:
            print(f"   ❌ 图标文件不存在: {full_icon_path}")

    if not icon_found:
        # 移除图标参数
        cmd = [arg for arg in cmd if not arg.startswith('--icon=')]
        print("   ⚠️  未找到图标文件，将使用默认图标")

    # 显示最终的打包命令
    print(f"   📋 打包命令: {' '.join(cmd)}")

    try:
        # 执行打包命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("   ✅ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 构建失败: {e}")
        print(f"   错误输出: {e.stderr}")
        return False

def copy_additional_files():
    """复制额外文件到dist目录"""
    print("📁 复制额外文件...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("   ❌ dist目录不存在")
        return False
    
    # 不需要复制任何文件，所有数据都已内置
    files_to_copy = []

    # 不复制目录，程序会自动创建需要的目录
    dirs_to_copy = []
    
    # 复制文件
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir)
            print(f"   ✅ 复制文件: {file_name}")
    
    # 复制目录
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            dest_dir = dist_dir / dir_name
            if dest_dir.exists():
                shutil.rmtree(dest_dir)
            shutil.copytree(dir_name, dest_dir)
            print(f"   ✅ 复制目录: {dir_name}")
    
    return True

def create_readme():
    """创建使用说明"""
    print("📝 创建使用说明...")
    
    readme_content = """# 登记设立批量查询 v1.0.0

## 使用说明

1. 双击 `登记设立批量查询.exe` 启动程序
2. 配置代理设置（必需）
3. 点击"开始查询"按钮开始批量查询
4. 查询结果将保存在 `extracted_data.json` 文件中

## 注意事项

- 程序每天最多可以查询4次
- 必须启用代理才能正常使用
- 查询过程中请保持网络连接稳定

## 文件说明

- `登记设立批量查询.exe` - 主程序
- `config/` - 配置文件目录
- `resources/` - 资源文件目录
- `region_code.json` - 地区代码文件
- `extracted_data.json` - 查询结果文件（运行后生成）

## 技术支持

如有问题请联系技术支持。
"""
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("   ✅ 创建使用说明.txt")

def main():
    """主函数"""
    print("=" * 60)
    print("登记设立批量查询 - 程序打包工具")
    print("=" * 60)

    # 获取脚本所在目录的父目录（项目根目录）
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)

    print(f"📁 脚本目录: {script_dir}")
    print(f"📁 项目根目录: {project_root}")

    # 切换到项目根目录
    os.chdir(project_root)
    print(f"📁 当前工作目录: {os.getcwd()}")

    # 1. 清理构建目录
    clean_build()

    # 2. 检查依赖
    if not check_requirements():
        return False

    # 3. 构建可执行文件
    if not build_exe():
        return False

    # 4. 复制额外文件
    if not copy_additional_files():
        return False

    # 5. 创建使用说明
    create_readme()

    print("\n" + "=" * 60)
    print("🎉 打包完成!")
    print("=" * 60)
    print("📁 输出目录: dist/")
    print("📦 可执行文件: dist/登记设立批量查询.exe")
    print("📝 使用说明: dist/使用说明.txt")

    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 用户取消打包")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
