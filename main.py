#!/usr/bin/env python3
"""
登记设立批量查询主入口程序
"""
import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

# 添加项目路径到sys.path
if getattr(sys, 'frozen', False):
    # 如果是打包后的exe
    project_root = sys._MEIPASS
else:
    # 如果是开发环境
    project_root = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, project_root)

try:
    from utils.singleton import check_single_instance, show_already_running_dialog
    # from utils.usage_limiter import check_usage_limit, show_usage_limit_dialog, record_app_run
    from utils.logger import setup_logger
    from config.settings import setup_settings
    from gui.main_window import MainWindow
    from gui.styles import PREFERRED_FONTS
except ImportError as e:
    print(f"模块导入失败: {e}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    print(f"sys.path: {sys.path}")
    raise


def setup_application():
    """设置应用程序"""
    # 禁用自动DPI缩放，手动控制窗口大小
    QApplication.setAttribute(Qt.AA_DisableHighDpiScaling, True)

    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("登记设立批量查询")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("登记设立批量查询")
    app.setOrganizationDomain("registration-query.local")

    # 设置应用程序图标
    setup_app_icon(app)

    # 设置字体
    setup_fonts(app)

    return app


def setup_app_icon(app):
    """设置应用程序图标"""
    try:
        # 获取程序运行目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe
            base_path = sys._MEIPASS
        else:
            # 如果是开发环境
            base_path = project_root

        # 尝试加载图标文件
        icon_paths = [
            os.path.join(base_path, "resources", "icons", "app.ico"),
            os.path.join(base_path, "resources", "icons", "学校查询.ico"),
            os.path.join(base_path, "resources", "icons", "app.png"),
            # 也尝试当前目录
            "resources/icons/app.ico",
            "resources/icons/学校查询.ico"
        ]

        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                if not icon.isNull():
                    app.setWindowIcon(icon)
                    print(f"✅ 成功设置应用程序图标: {icon_path}")
                    return

        print("⚠️ 未找到应用程序图标文件")

    except Exception as e:
        print(f"设置应用程序图标失败: {e}")


def setup_fonts(app):
    """设置应用程序字体"""
    try:
        # 尝试设置首选字体
        for font_family in PREFERRED_FONTS:
            font = QFont(font_family, 9)
            if font.exactMatch():
                app.setFont(font)
                print(f"使用字体: {font_family}")
                break
        else:
            # 如果没有找到首选字体，使用系统默认字体
            font = QFont()
            font.setPointSize(9)
            app.setFont(font)
            print("使用系统默认字体")

    except Exception as e:
        print(f"设置字体失败: {e}")


def check_prerequisites():
    """检查运行前提条件"""
    # 暂时禁用单实例检查
    # can_run, instance_info = check_single_instance("GuangdongCollector")
    # if not can_run:
    #     show_already_running_dialog()
    #     return False

    # 使用次数限制功能已删除

    return True


def setup_environment():
    """设置运行环境"""
    try:
        # 设置工作目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe，工作目录设置为exe文件所在目录
            exe_dir = os.path.dirname(sys.executable)
            os.chdir(exe_dir)
            print(f"打包环境，工作目录设置为: {exe_dir}")
        else:
            # 如果是开发环境，工作目录设置为项目根目录
            os.chdir(project_root)
            print(f"开发环境，工作目录设置为: {project_root}")

        current_dir = os.getcwd()
        print(f"当前工作目录: {current_dir}")

        # 创建必要的目录
        directories = ['logs', 'config', 'data_backup']
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"创建目录: {directory}")

        # 初始化配置系统
        settings = setup_settings()
        print("配置系统初始化完成")

        # 初始化日志系统
        logger = setup_logger()
        logger.info("应用程序启动")
        logger.info(f"工作目录: {current_dir}")

        return True

    except Exception as e:
        print(f"设置运行环境失败: {e}")
        return False


def main():
    """主函数"""
    app = None
    try:
        print("=" * 60)
        print("登记设立批量查询 v1.0.0")
        print("=" * 60)

        # 检查运行前提条件
        print("检查运行前提条件...")
        if not check_prerequisites():
            print("前提条件检查失败，程序退出")
            return 1

        # 设置运行环境
        print("设置运行环境...")
        if not setup_environment():
            print("运行环境设置失败，程序退出")
            return 1

        # 创建应用程序
        print("初始化应用程序...")
        app = setup_application()

        # 设置全局异常处理器
        def handle_exception(exc_type, exc_value, exc_traceback):
            """全局异常处理器"""
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            import traceback
            error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            print(f"未捕获的异常:\n{error_msg}")

            # 尝试写入日志文件
            try:
                import datetime
                log_file = f"crash_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                with open(log_file, 'w', encoding='utf-8') as f:
                    f.write(f"程序崩溃日志 - {datetime.datetime.now()}\n")
                    f.write("=" * 60 + "\n")
                    f.write(error_msg)
                    f.write("\n" + "=" * 60 + "\n")
                print(f"崩溃日志已保存到: {log_file}")
            except:
                pass

            # 尝试显示错误对话框
            try:
                from PyQt5.QtWidgets import QMessageBox, QApplication
                if QApplication.instance():
                    msg = QMessageBox()
                    msg.setIcon(QMessageBox.Critical)
                    msg.setWindowTitle("程序异常")
                    msg.setText("程序遇到未处理的异常，即将退出。")
                    msg.setDetailedText(error_msg)
                    msg.exec_()
            except:
                pass

        sys.excepthook = handle_exception

        # 创建主窗口
        print("创建主窗口...")
        main_window = MainWindow()

        # 显示主窗口
        main_window.show()

        print("应用程序启动完成")
        print("=" * 60)

        # 运行应用程序
        return app.exec_()

    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 0

    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

        # 尝试显示错误对话框
        try:
            if app:
                from PyQt5.QtWidgets import QMessageBox
                msg = QMessageBox()
                msg.setIcon(QMessageBox.Critical)
                msg.setWindowTitle("启动失败")
                msg.setText(f"程序启动失败:\n{str(e)}")
                msg.setDetailedText(traceback.format_exc())
                msg.exec_()
        except:
            pass

        return 1

if __name__ == "__main__":
    sys.exit(main())
