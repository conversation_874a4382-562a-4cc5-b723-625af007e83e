#!/usr/bin/env python3
"""
测试定时运行逻辑
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTime
from config.settings import get_settings
from datetime import datetime

def test_time_matching():
    """测试时间匹配逻辑"""
    print("=" * 50)
    print("测试时间匹配逻辑")
    print("=" * 50)
    
    # 获取当前时间
    current_time = QTime.currentTime()
    print(f"当前时间: {current_time.toString('HH:mm:ss')}")
    
    # 测试不同的设置时间
    test_times = ["06:00", "08:30", "12:00", "18:00", current_time.toString('HH:mm')]
    
    for time_str in test_times:
        scheduled_time = QTime.fromString(time_str, 'HH:mm')
        is_match = (current_time.hour() == scheduled_time.hour() and 
                   current_time.minute() == scheduled_time.minute())
        
        print(f"设置时间: {time_str} -> 匹配: {is_match}")
    
    print(f"\n今天日期: {datetime.now().date()}")

def test_config_update():
    """测试配置更新"""
    print("\n" + "=" * 50)
    print("测试配置更新")
    print("=" * 50)
    
    settings = get_settings()
    
    # 临时启用定时运行
    original_enabled = settings.get('app.scheduled_run_enabled', False)
    original_time = settings.get('app.scheduled_run_time', '06:00')
    
    print(f"原始配置 - 启用: {original_enabled}, 时间: {original_time}")
    
    # 设置为当前时间的下一分钟
    current_time = QTime.currentTime()
    next_minute = current_time.addSecs(60)
    test_time = next_minute.toString('HH:mm')
    
    settings.set('app.scheduled_run_enabled', True)
    settings.set('app.scheduled_run_time', test_time)
    settings.save_config()
    
    print(f"测试配置 - 启用: True, 时间: {test_time}")
    
    # 验证配置已保存
    new_enabled = settings.get('app.scheduled_run_enabled')
    new_time = settings.get('app.scheduled_run_time')
    print(f"验证配置 - 启用: {new_enabled}, 时间: {new_time}")
    
    # 恢复原始配置
    settings.set('app.scheduled_run_enabled', original_enabled)
    settings.set('app.scheduled_run_time', original_time)
    settings.save_config()
    
    print(f"恢复配置 - 启用: {original_enabled}, 时间: {original_time}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    test_time_matching()
    test_config_update()
    print("\n测试完成")
