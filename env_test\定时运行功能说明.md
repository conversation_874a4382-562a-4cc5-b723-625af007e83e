# 定时运行功能使用说明

## 功能概述

新增的定时运行功能允许用户设置一个固定时间，程序会在每天的指定时间自动开始查询任务，无需手动点击"开始查询"按钮。

## 功能特点

- ✅ **简单配置**: 只需一个复选框和一个时间设置框
- ✅ **自由设置**: 用户可以自由设置任意时间（24小时格式）
- ✅ **计入限制**: 定时运行同样计入每天4次的使用限制
- ✅ **防重复**: 每天只会在设定时间运行一次
- ✅ **智能检查**: 自动检查使用次数和运行状态
- ✅ **主界面集成**: 定时设置直接显示在主界面的配置设置区域

## 使用方法

### 1. 找到定时运行设置
在主窗口的"配置设置"区域中，你会看到：
- ☑️ "启用定时运行"复选框
- 🕐 "运行时间"时间选择框

### 2. 设置定时运行
- ☑️ 勾选"启用定时运行"复选框
- 🕐 在"运行时间"框中设置时间（格式：HH:MM）
- 💾 设置会自动保存，无需额外操作

### 3. 时间格式说明
- 使用24小时制格式
- 例如：06:00（早上6点）、18:30（晚上6点30分）
- 默认时间为06:00

## 运行逻辑

### 定时检查
- 程序每分钟检查一次当前时间
- 当时间匹配设定时间时（精确到分钟），自动触发查询

### 限制检查
1. **启用状态**: 只有勾选"启用定时运行"时才会执行
2. **使用次数**: 检查当天是否已达4次运行限制
3. **运行状态**: 检查是否已有查询任务在运行
4. **重复检查**: 确保每天只在设定时间运行一次

### 日志记录
- ⏰ 定时运行开始：`"⏰ 定时运行开始（HH:MM）"`
- ⚠️ 超出限制：`"⏰ 定时运行跳过，今日已达运行次数限制"`
- ℹ️ 正在运行：`"⏰ 定时运行跳过，查询正在进行中"`

## 配置文件

定时运行设置保存在 `config/config.json` 文件中：

```json
{
  "app": {
    "scheduled_run_enabled": false,  // 是否启用定时运行
    "scheduled_run_time": "06:00"    // 定时运行时间
  }
}
```

## 注意事项

1. **程序必须保持运行**: 定时功能只在程序运行时有效
2. **计入使用限制**: 定时运行会消耗每日4次的使用次数
3. **网络要求**: 定时运行时同样需要网络连接和代理设置
4. **时间精度**: 定时检查精确到分钟，在设定时间的那一分钟内触发

## 测试方法

可以设置一个几分钟后的时间来测试功能：
1. 设置定时时间为当前时间+2分钟
2. 勾选启用定时运行
3. 保存配置
4. 等待定时触发，观察日志输出

## 故障排除

### 定时运行没有触发
- 检查是否勾选了"启用定时运行"
- 确认时间设置正确（24小时制）
- 检查是否已达每日使用限制
- 确保程序保持运行状态

### 定时运行被跳过
- 查看日志中的具体原因
- 可能是使用次数已满或正在查询中

## 技术实现

- 使用 `QTimer` 每分钟检查一次
- 使用 `QTime` 进行时间比较
- 集成现有的使用限制和查询系统
- 线程安全的日志记录
