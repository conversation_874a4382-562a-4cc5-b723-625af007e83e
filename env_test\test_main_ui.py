#!/usr/bin/env python3
"""
测试主界面定时运行功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTime
from config.settings import get_settings

def test_scheduler_ui():
    """测试主界面定时运行功能"""
    print("=" * 50)
    print("测试主界面定时运行功能")
    print("=" * 50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 获取设置
    settings = get_settings()
    
    # 显示当前配置
    print(f"当前定时运行状态: {settings.get('app.scheduled_run_enabled', False)}")
    print(f"当前定时运行时间: {settings.get('app.scheduled_run_time', '06:00')}")
    
    # 导入主窗口
    from gui.main_window import MainWindow
    
    # 创建主窗口
    main_window = MainWindow()
    
    # 显示主窗口
    main_window.show()
    
    print("\n主窗口已打开，请检查定时运行设置是否显示在配置设置区域")
    print("- 应该能看到'启用定时运行'复选框")
    print("- 应该能看到'运行时间'时间选择框")
    print("- 可以测试勾选复选框和修改时间")
    print("\n关闭窗口结束测试...")
    
    # 运行应用程序
    return app.exec_()

if __name__ == "__main__":
    test_scheduler_ui()
