import requests
import re
import json
import time
import random

headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'no-cache',
    'Content-Type': 'application/x-www-form-urlencoded',
    'DNT': '1',
    'Origin': 'http://search.gjsy.gov.cn',
    'Pragma': 'no-cache',
    'Proxy-Connection': 'keep-alive',
    'Referer': 'http://search.gjsy.gov.cn/index.jsp?t=12&c=440104&at=10&sn=1&mc=20',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
}

def load_region_codes():
    """
    从region_code.json文件中加载地区代码和名称
    """
    try:
        # 尝试使用utf-8-sig编码来处理BOM
        with open('region_code.json', 'r', encoding='utf-8-sig') as f:
            data = json.load(f)
            return data['region_code']
    except Exception as e:
        try:
            # 如果utf-8-sig失败，尝试普通utf-8
            with open('region_code.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data['region_code']
        except Exception as e2:
            print(f"读取region_code.json文件时出错: {e2}")
            return []

def load_existing_data():
    """
    加载已存在的JSON数据
    """
    try:
        with open('extracted_data.json', 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:  # 文件为空
                return []
            return json.loads(content)
    except FileNotFoundError:
        return []
    except json.JSONDecodeError:
        print("extracted_data.json文件格式错误，将重新创建")
        return []
    except Exception as e:
        print(f"读取existing data时出错: {e}")
        return []

def save_data_to_json(new_data, existing_data):
    """
    将新数据追加到现有数据并保存到JSON文件
    """
    try:
        # 合并数据
        all_data = existing_data + new_data

        with open('extracted_data.json', 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)

        print(f"  已保存 {len(new_data)} 条新记录，总计 {len(all_data)} 条记录")
        return all_data
    except Exception as e:
        print(f"  保存数据时出错: {e}")
        return existing_data

def query_region_data(region_code, region_name, max_retries=3):
    """
    查询指定地区的数据，带重试机制
    """
    for attempt in range(max_retries):
        try:
            # 生成当前时间戳（毫秒级）
            current_timestamp = str(int(time.time() * 1000))

            data = {
                'c': region_code,
                't': '12',
                'at': '0',  # 设立登记
                'sn': '1',
                'mc': '20',
                'ts': current_timestamp,
                'w': 'null',
                'atRadio': '0',
                'h': 'null'
            }

            print(f"正在查询 {region_name} ({region_code})，第 {attempt + 1} 次尝试...")

            response = requests.post('http://search.gjsy.gov.cn/bjtz/query',
                                   headers=headers, data=data, verify=False, timeout=10)

            if response.status_code == 200:
                # 提取数据
                extracted_data = extract_data_from_html(response.text)

                # 筛选包含"中学"或"学校"的记录
                school_data = []
                for item in extracted_data:
                    unit_name = item['单位名称']
                    if "中学" in unit_name or "学校" in unit_name:
                        # 添加地区信息
                        item['地区代码'] = region_code
                        item['地区名称'] = region_name
                        school_data.append(item)

                print(f"  成功获取数据，找到 {len(school_data)} 条学校相关记录")
                return school_data

        except Exception as e:
            print(f"  第 {attempt + 1} 次尝试失败: {e}")
            if attempt < max_retries - 1:
                wait_time = random.uniform(2, 5)  # 失败时等待2-5秒
                print(f"  等待 {wait_time:.1f} 秒后重试...")
                time.sleep(wait_time)

    print(f"  {region_name} ({region_code}) 查询失败，已达到最大重试次数")
    return []

def get_application_type(type_code):
    """
    根据申办事项类型代码返回对应的中文名称
    """
    type_mapping = {
        "0": "设立登记",
        "1": "变更登记",
        "3": "注销登记",
        "4": "证书补领",
        "5": "重新申领证书"
    }
    return type_mapping.get(type_code, "未知类型")

def extract_data_from_html(html_content):
    """
    从HTML内容中提取申办事项数据
    """
    # 使用正则表达式提取JavaScript数组中的数据
    # 匹配 d.push("值"); 的模式
    pattern = r'd\.push\("([^"]*)"\);'
    matches = re.findall(pattern, html_content)

    # 每6个元素为一组数据
    data_list = []
    for i in range(0, len(matches), 6):
        if i + 5 < len(matches):
            item = {
                "申办事项": get_application_type(matches[i]),
                "单位名称": matches[i + 1],
                "统一社会信用代码": matches[i + 2],
                # 跳过状态字段 matches[i + 3]
                "通知时间": matches[i + 4] if matches[i + 4] else matches[i + 5]
            }
            data_list.append(item)

    return data_list

# 主程序：批量查询所有地区
def main():
    print("开始批量查询广东省各地区学校数据...")
    print("=" * 80)

    # 加载地区代码
    regions = load_region_codes()
    if not regions:
        print("无法加载地区代码，程序退出")
        return

    print(f"共加载 {len(regions)} 个地区代码")

    # 加载已存在的数据
    existing_data = load_existing_data()
    print(f"已存在 {len(existing_data)} 条记录")
    print("=" * 80)

    success_count = 0
    total_new_records = 0

    for i, region in enumerate(regions, 1):
        region_code = region['code']
        region_name = region['name']

        print(f"[{i}/{len(regions)}] 查询地区: {region_name} ({region_code})")

        # 查询该地区的数据
        school_data = query_region_data(region_code, region_name)

        if school_data:
            # 立即保存到JSON文件
            existing_data = save_data_to_json(school_data, existing_data)
            success_count += 1
            total_new_records += len(school_data)
            print(f"  本次查询新增 {len(school_data)} 条记录")
        else:
            print(f"  本次查询未找到学校记录")

        # 成功获取数据后随机延迟1-3秒
        if i < len(regions):  # 不是最后一个就延迟
            delay_time = random.uniform(1, 3)
            print(f"  等待 {delay_time:.1f} 秒...")
            time.sleep(delay_time)

        print("-" * 60)

    # 显示汇总结果
    print("=" * 80)
    print("批量查询完成！")
    print(f"成功查询地区数: {success_count}/{len(regions)}")
    print(f"本次新增学校记录: {total_new_records} 条")
    print(f"总共学校记录: {len(existing_data)} 条")
    print("=" * 80)
    print("所有数据已实时保存到 extracted_data.json 文件中！")

if __name__ == "__main__":
    main()
