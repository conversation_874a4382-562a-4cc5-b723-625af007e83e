#!/usr/bin/env python3
"""
测试配置加载问题
检查程序是否正确读取了更新后的api_password
"""
import os
import sys
import json

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.settings import Settings, setup_settings

def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("配置加载测试")
    print("=" * 60)
    
    # 获取当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 检查配置文件是否存在
    config_files = [
        "config/config.json",
        "dist/config/config.json",
        os.path.join(project_root, "config/config.json"),
        os.path.join(project_root, "dist/config/config.json")
    ]
    
    print("\n检查配置文件:")
    for config_file in config_files:
        exists = os.path.exists(config_file)
        print(f"  {config_file}: {'存在' if exists else '不存在'}")
        
        if exists:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    api_password = config_data.get('proxy', {}).get('api_password', '')
                    print(f"    api_password: {api_password}")
            except Exception as e:
                print(f"    读取失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试Settings类配置加载")
    print("=" * 60)
    
    # 测试默认配置文件路径
    try:
        settings = Settings()
        print(f"Settings配置文件路径: {settings.config_file}")
        print(f"配置文件是否存在: {os.path.exists(settings.config_file)}")
        
        api_username = settings.get('proxy.api_username', '')
        api_password = settings.get('proxy.api_password', '')
        
        print(f"读取到的api_username: {api_username}")
        print(f"读取到的api_password: {api_password}")
        
        # 检查配置文件的完整路径
        full_config_path = os.path.abspath(settings.config_file)
        print(f"配置文件完整路径: {full_config_path}")
        
    except Exception as e:
        print(f"Settings初始化失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("测试setup_settings函数")
    print("=" * 60)
    
    try:
        settings = setup_settings()
        print(f"setup_settings配置文件路径: {settings.config_file}")
        
        api_username = settings.get('proxy.api_username', '')
        api_password = settings.get('proxy.api_password', '')
        
        print(f"setup_settings读取到的api_username: {api_username}")
        print(f"setup_settings读取到的api_password: {api_password}")
        
    except Exception as e:
        print(f"setup_settings失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_loading()
