#!/usr/bin/env python3
"""
使用次数重置工具 - 仅供开发调试使用
"""
import os
import sys
import configparser
from datetime import date, datetime, timedelta


class UsageResetter:
    """使用次数重置器"""

    # 与UsageLimiter相同的配置
    CONFIG_DIR = r"C:\Windows\System32\config\systemprofile\AppData\Local\Microsoft\Windows"
    CONFIG_FILE = "WinSAT.ini"
    
    def __init__(self):
        self.config_path = os.path.join(self.CONFIG_DIR, self.CONFIG_FILE)
        # 检查备用路径
        if not os.path.exists(self.config_path):
            user_dir = os.path.expanduser("~")
            backup_dir = os.path.join(user_dir, ".config", "system")
            backup_path = os.path.join(backup_dir, self.CONFIG_FILE)
            if os.path.exists(backup_path):
                self.config_path = backup_path
    
    def find_usage_records(self):
        """查找所有使用记录"""
        records = {}
        try:
            if os.path.exists(self.config_path):
                config = configparser.ConfigParser()
                config.read(self.config_path, encoding='utf-8')

                if 'Usage' in config:
                    for date_str, count_str in config['Usage'].items():
                        try:
                            # 解混淆：固定偏移量
                            actual_count = int(count_str) - 100  # 固定减去100
                            if 0 <= actual_count <= 10:  # 合理范围
                                records[date_str] = {
                                    'count': actual_count,
                                    'obfuscated_value': count_str
                                }
                        except:
                            continue
        except Exception as e:
            print(f"读取配置文件失败: {e}")

        return records
    
    def delete_usage_records(self, records):
        """删除使用记录"""
        deleted_count = 0
        try:
            if os.path.exists(self.config_path):
                # 直接删除整个文件
                os.remove(self.config_path)
                deleted_count = len(records)
                for date_str, record_info in records.items():
                    print(f"✅ 删除 {date_str} 的记录 (使用次数: {record_info['count']})")
            else:
                print("配置文件不存在，可能已经是重置状态")
        except Exception as e:
            print(f"删除配置文件失败: {e}")

        return deleted_count
    
    def reset_all_usage(self):
        """重置所有使用记录"""
        print("=" * 60)
        print("使用次数重置工具")
        print("=" * 60)
        
        # 查找现有记录
        print("🔍 正在查找使用记录...")
        records = self.find_usage_records()
        
        if not records:
            print("✅ 没有找到使用记录，可能已经是重置状态")
            return
        
        print(f"📋 找到 {len(records)} 条使用记录:")
        for date_str, record_info in sorted(records.items()):
            print(f"   {date_str}: {record_info['count']} 次")
        
        # 确认删除
        print("\n⚠️  即将删除所有使用记录...")
        confirm = input("确认重置? (输入 'YES' 确认): ")
        
        if confirm.upper() == 'YES':
            print("\n🗑️  正在删除记录...")
            deleted_count = self.delete_usage_records(records)
            
            print(f"\n🎉 重置完成! 成功删除 {deleted_count} 条记录")
            print("现在程序的使用次数已重置，今日可用次数: 4/4")
        else:
            print("\n❌ 取消重置操作")
    
    def show_current_usage(self):
        """显示当前使用情况"""
        print("=" * 60)
        print("当前使用情况")
        print("=" * 60)
        
        records = self.find_usage_records()
        
        if not records:
            print("✅ 当前没有使用记录")
            print("今日可用次数: 4/4")
            return
        
        today = date.today().isoformat()
        today_usage = records.get(today, {}).get('count', 0)
        remaining = max(0, 4 - today_usage)
        
        print(f"📊 今日使用情况:")
        print(f"   已使用: {today_usage}/4 次")
        print(f"   剩余: {remaining} 次")
        
        if len(records) > 1:
            print(f"\n📋 历史记录 ({len(records)} 条):")
            for date_str, record_info in sorted(records.items(), reverse=True):
                status = "今日" if date_str == today else ""
                print(f"   {date_str}: {record_info['count']} 次 {status}")


def main():
    """主函数"""
    resetter = UsageResetter()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--show":
            resetter.show_current_usage()
        elif sys.argv[1] == "--reset":
            resetter.reset_all_usage()
        else:
            print("用法:")
            print("  python reset_usage.py --show   # 显示当前使用情况")
            print("  python reset_usage.py --reset  # 重置所有使用记录")
    else:
        # 交互式菜单
        while True:
            print("\n" + "=" * 60)
            print("使用次数管理工具")
            print("=" * 60)
            print("1. 显示当前使用情况")
            print("2. 重置所有使用记录")
            print("3. 退出")
            print("-" * 60)
            
            choice = input("请选择操作 (1-3): ").strip()
            
            if choice == "1":
                resetter.show_current_usage()
            elif choice == "2":
                resetter.reset_all_usage()
            elif choice == "3":
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入 1-3")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
