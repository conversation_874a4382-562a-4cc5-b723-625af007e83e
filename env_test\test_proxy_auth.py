#!/usr/bin/env python3
"""
测试代理认证修复
"""
import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.proxy_manager import create_proxy_manager
from config.settings import get_settings

def test_proxy_auth():
    """测试代理认证"""
    print("=" * 60)
    print("测试代理认证修复")
    print("=" * 60)
    
    # 获取配置
    settings = get_settings()
    proxy_config = settings.get_section('proxy')
    
    print(f"代理配置:")
    print(f"  enabled: {proxy_config.get('enabled')}")
    print(f"  api_username: {proxy_config.get('api_username')}")
    print(f"  api_password: {proxy_config.get('api_password')}")
    
    # 创建代理管理器
    proxy_manager = create_proxy_manager(proxy_config, None)
    
    if proxy_manager:
        print("\n开始测试代理获取...")
        
        # 尝试获取代理
        proxy = proxy_manager.get_proxy()
        
        if proxy:
            print(f"✅ 成功获取代理: {proxy}")
            
            # 测试代理是否包含认证信息
            http_proxy = proxy.get('http', '')
            if '@' in http_proxy:
                print("✅ 代理包含认证信息")
            else:
                print("⚠️ 代理不包含认证信息")
                
        else:
            print("❌ 获取代理失败")
    else:
        print("❌ 代理管理器创建失败")

if __name__ == "__main__":
    test_proxy_auth()
