"""
PyInstaller打包配置文件
"""
import os
import sys

# 项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 添加项目路径
sys.path.insert(0, project_root)

# 数据文件列表
datas = [
    # 配置文件
    (os.path.join(project_root, 'region_code.json'), '.'),
    
    # 资源文件（如果存在）
    # (os.path.join(project_root, 'resources'), 'resources'),
]

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'requests',
    'json',
    'time',
    'random',
    're',
    'os',
    'sys',
    'datetime',
    'urllib.parse',
    'logging',
    'tempfile',
    'shutil',
    'csv',
    'hashlib',
    'platform',
    'uuid',
    'psutil'
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'PIL',
    'cv2',
    'tensorflow',
    'torch',
    'sklearn'
]

# PyInstaller配置
a = Analysis(
    [os.path.join(project_root, 'main.py')],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='登记设立批量查询',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    icon='resources/icons/学校查询.svg',  # 应用程序图标
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='登记设立批量查询',
)
