#!/usr/bin/env python3
"""
测试代理日志显示
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_proxy_url_parsing():
    """测试代理URL解析"""
    print("=" * 50)
    print("测试代理URL解析")
    print("=" * 50)
    
    # 测试不同格式的代理URL
    test_urls = [
        "http://ok2ybrbcxu1r4ztfpfvf:yzsvr0e4n35tmvz9fu33mjm0pjh26o47@**********:20562",
        "http://**********:20562",
        "**********************************",
        "http://***********:8080",
        "直连",
        ""
    ]
    
    for proxy_url in test_urls:
        print(f"\n原始URL: {proxy_url}")
        
        if proxy_url and proxy_url != '直连':
            # 从代理URL中提取IP:端口部分
            if '@' in proxy_url:
                # 有认证信息，提取@后面的部分
                ip_port = proxy_url.split('@')[-1]
            else:
                # 无认证信息，提取://后面的部分
                ip_port = proxy_url.split('://')[-1] if '://' in proxy_url else proxy_url
            print(f"显示结果: 🌐 使用代理: {ip_port}")
        else:
            print(f"显示结果: 🌐 使用代理: {proxy_url}")

if __name__ == "__main__":
    test_proxy_url_parsing()
