# 登记设立批量查询打包说明

## 环境要求

- Python 3.7+
- Windows 10/11
- 至少2GB可用磁盘空间

## 依赖包安装

```bash
pip install -r requirements.txt
```

## 打包步骤

### 方法1：使用批处理脚本（推荐）

1. 双击运行 `build.bat`
2. 脚本会自动检查环境、安装依赖、执行打包
3. 打包完成后会在 `package/dist/登记设立批量查询/` 目录生成可执行文件

### 方法2：手动打包

```bash
# 切换到项目根目录
cd /d "项目根目录"

# 执行打包命令
pyinstaller --onedir --windowed --name="登记设立批量查询" --distpath="package/dist" --workpath="package/build" --specpath="package" --add-data="region_code.json;." --hidden-import="PyQt5.QtCore" --hidden-import="PyQt5.QtGui" --hidden-import="PyQt5.QtWidgets" --hidden-import="requests" --hidden-import="psutil" --exclude-module="tkinter" --exclude-module="matplotlib" --exclude-module="numpy" --exclude-module="pandas" --noconfirm main.py
```

## 打包输出

打包完成后，会在 `package/dist/登记设立批量查询/` 目录下生成：

- `登记设立批量查询.exe` - 主程序
- `_internal/` - 依赖库文件夹
- `region_code.json` - 地区代码配置文件
- `启动.bat` - 启动脚本

## 分发说明

1. 将整个 `登记设立批量查询` 文件夹复制到目标机器
2. 双击 `登记设立批量查询.exe` 或 `启动.bat` 运行程序
3. 首次运行会创建配置文件和日志目录

## 注意事项

1. **不要删除 `_internal` 文件夹**，这是程序运行必需的依赖文件
2. **保留 `region_code.json` 文件**，这是地区代码配置文件
3. 程序会在运行目录下创建以下文件夹：
   - `logs/` - 日志文件
   - `config/` - 配置文件
   - `data_backup/` - 数据备份

## 故障排除

### 打包失败

1. 检查Python版本是否为3.7+
2. 确保所有依赖包已正确安装
3. 检查磁盘空间是否充足
4. 尝试以管理员权限运行打包脚本

### 运行时错误

1. 确保目标机器有足够的内存（建议4GB+）
2. 检查Windows版本兼容性
3. 确保没有杀毒软件误报
4. 查看日志文件获取详细错误信息

### 功能异常

1. 检查网络连接
2. 确认地区代码文件格式正确
3. 检查配置文件设置
4. 查看程序日志排查问题

## 技术支持

如遇到问题，请提供以下信息：

1. 操作系统版本
2. 错误截图或日志文件
3. 具体操作步骤
4. 程序版本信息
