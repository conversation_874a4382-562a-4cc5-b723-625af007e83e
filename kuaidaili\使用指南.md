# 快代理SDK使用指南

## 🎯 项目背景

这个快代理SDK是从**中国社会组织监控工具**项目中提取出来的独立模块，经过重构和优化，提供了完整的快代理API集成功能。

## ✨ 主要特性

- ✅ **完整的API集成**: 支持快代理官方API的所有功能
- ✅ **智能代理验证**: 内置多重验证机制，确保代理可用性
- ✅ **代理池管理**: 自动管理代理池，支持轮换和刷新
- ✅ **灵活的配置**: 支持多种配置方式（文件、环境变量、字典）
- ✅ **完善的错误处理**: 详细的异常分类和处理机制
- ✅ **易于集成**: 简洁的API设计，易于在其他项目中使用

## 🚀 快速开始

### 1. 基本使用

```python
from kuaidaili_sdk import KuaidailiClient

# 创建客户端
client = KuaidailiClient(
    secret_id="your_secret_id",
    signature="your_signature"
)

# 获取单个代理
proxy = client.get_proxy()
print(f"获取到代理: {proxy}")

# 获取验证过的代理
verified_proxy = client.get_verified_proxy()
print(f"验证通过的代理: {verified_proxy}")
```

### 2. 使用代理池

```python
from kuaidaili_sdk import KuaidailiClient, ProxyPool

client = KuaidailiClient(secret_id="xxx", signature="xxx")

# 创建代理池
proxy_pool = ProxyPool(
    client=client,
    pool_size=20,        # 代理池大小
    auto_refresh=True,   # 自动刷新
    refresh_interval=300 # 5分钟刷新一次
)

# 从代理池获取代理
proxy = proxy_pool.get_proxy()
print(f"从代理池获取: {proxy}")
```

### 3. 使用代理会话

```python
# 自动管理代理的HTTP会话
with proxy_pool.get_session() as session:
    response = session.get("http://httpbin.org/ip")
    print(response.json())
```

## 📁 文件结构

```
kuaidaili_sdk/
├── __init__.py          # 包初始化，导出主要类
├── client.py            # 快代理API客户端
├── proxy_pool.py        # 代理池管理
├── validator.py         # 代理验证器
├── config.py            # 配置管理
├── exceptions.py        # 异常定义
├── examples.py          # 使用示例
├── test_sdk.py          # 单元测试
├── setup.py             # 安装脚本
├── README.md            # 详细文档
└── 使用指南.md          # 本文档
```

## 🔧 核心组件

### KuaidailiClient
- 快代理API的主要客户端
- 负责API调用、代理获取和验证
- 支持多种创建方式

### ProxyPool
- 代理池管理器
- 自动维护代理池，支持轮换和刷新
- 智能失败检测和恢复

### ProxyValidator
- 代理验证器
- 多重验证机制（连接测试、内容验证）
- 支持批量验证

### KuaidailiConfig
- 配置管理器
- 支持多种配置来源
- 配置验证和错误处理

## 💡 使用建议

### 1. 在爬虫项目中使用

```python
import requests
from kuaidaili_sdk import KuaidailiClient, ProxyPool

# 初始化
client = KuaidailiClient.from_config_file("config.ini")
proxy_pool = ProxyPool(client, pool_size=50)

# 在爬虫中使用
def crawl_with_proxy(url):
    with proxy_pool.get_session() as session:
        try:
            response = session.get(url, timeout=10)
            return response.text
        except Exception as e:
            print(f"请求失败: {e}")
            return None
```

### 2. 在API服务中使用

```python
from flask import Flask, jsonify
from kuaidaili_sdk import KuaidailiClient

app = Flask(__name__)
client = KuaidailiClient.from_env()

@app.route('/get_proxy')
def get_proxy():
    proxy = client.get_verified_proxy()
    if proxy:
        return jsonify({"proxy": proxy, "status": "success"})
    else:
        return jsonify({"error": "无法获取代理", "status": "failed"})
```

### 3. 在数据采集项目中使用

```python
from kuaidaili_sdk import KuaidailiClient, ProxyPool
import concurrent.futures

client = KuaidailiClient(secret_id="xxx", signature="xxx")
proxy_pool = ProxyPool(client, pool_size=100)

def fetch_data(url):
    proxy = proxy_pool.get_proxy()
    if proxy:
        proxy_config = client.get_proxy_config(proxy)
        # 使用代理进行数据采集
        # ...
    
# 并发采集
urls = ["http://example.com"] * 100
with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    results = list(executor.map(fetch_data, urls))
```

## ⚙️ 配置选项

### 配置文件格式 (config.ini)

```ini
[Kuaidaili]
secret_id = your_secret_id
signature = your_signature
```

### 环境变量

```bash
export KUAIDAILI_SECRET_ID=your_secret_id
export KUAIDAILI_SIGNATURE=your_signature
```

### 代码配置

```python
from kuaidaili_sdk import KuaidailiConfig

config = KuaidailiConfig.from_dict({
    'secret_id': 'your_secret_id',
    'signature': 'your_signature'
})
```

## 🔍 测试和验证

### 运行基本测试

```bash
cd kuaidaili_sdk
python test_sdk.py
```

### 运行集成测试

```bash
# 在项目根目录
python test_kuaidaili_integration.py
```

### 运行示例

```bash
cd kuaidaili_sdk
python examples.py
```

## 🚨 注意事项

1. **API凭证**: 需要有效的快代理API凭证
2. **网络环境**: 建议在稳定的网络环境下使用
3. **调用频率**: 注意API调用频率限制
4. **代理验证**: 验证会消耗额外时间，根据需要启用
5. **异常处理**: 做好异常处理和重试机制

## 🔄 从原项目迁移

如果你正在使用原项目中的代理功能，可以这样迁移：

### 原代码
```python
from core.proxy_manager import ProxyManager
from utils.config import ConfigManager

config_manager = ConfigManager()
proxy_manager = ProxyManager(config_manager)
proxy = proxy_manager.get_verified_proxy()
```

### 新代码
```python
from kuaidaili_sdk import KuaidailiClient

client = KuaidailiClient.from_config_file("config.ini")
proxy = client.get_verified_proxy()
```

## 📈 性能优化建议

1. **使用代理池**: 避免频繁获取新代理
2. **合理设置池大小**: 根据并发需求调整
3. **启用缓存**: 使用缓存减少API调用
4. **批量验证**: 一次验证多个代理
5. **异步处理**: 考虑使用异步版本（未来版本）

## 🤝 贡献和反馈

这个SDK是从实际项目中提取的，经过了充分的测试和验证。如果你在使用过程中遇到问题或有改进建议，欢迎反馈。

## 📄 许可证

本SDK提取自中国社会组织监控工具项目，仅供学习和研究使用。
