"""
快代理配置组件 - 可嵌入的Widget版本
可以嵌入到其他窗口中使用
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
                             QPushButton, QLabel, QCheckBox, QMessageBox,
                             QProgressBar, QGroupBox, QLineEdit, 
                             QSpinBox, QFormLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from ..client import KuaidailiClient
from ..config import KuaidailiConfig
from ..exceptions import APIException, ConfigException


class ProxyWidget(QWidget):
    """快代理配置组件 - 可嵌入版本"""
    
    # 信号定义
    config_changed = pyqtSignal(object)  # 配置变化信号
    status_changed = pyqtSignal(str)     # 状态变化信号

    def __init__(self, parent=None, config=None):
        super().__init__(parent)
        
        # 配置管理
        if config:
            self.config = config
        else:
            self.config = KuaidailiConfig()
        
        self.client = None
        self.init_ui()
        self.load_config()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 启用代理复选框
        self.cb_enable_proxy = QCheckBox("启用快代理")
        self.cb_enable_proxy.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        self.cb_enable_proxy.setStyleSheet("""
            QCheckBox {
                color: #007bff;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.cb_enable_proxy)

        # 快代理API配置组
        api_group = QGroupBox("快代理API配置")
        api_layout = QFormLayout(api_group)

        # Secret ID
        self.le_secret_id = QLineEdit()
        self.le_secret_id.setPlaceholderText("请输入快代理的secret_id")
        api_layout.addRow("Secret ID:", self.le_secret_id)

        # Signature
        self.le_signature = QLineEdit()
        self.le_signature.setPlaceholderText("请输入快代理的signature")
        api_layout.addRow("Signature:", self.le_signature)

        # 代理数量
        self.sb_proxy_count = QSpinBox()
        self.sb_proxy_count.setRange(1, 100)
        self.sb_proxy_count.setValue(10)
        api_layout.addRow("每次获取代理数量:", self.sb_proxy_count)

        layout.addWidget(api_group)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.btn_test_api = QPushButton("测试连接")
        self.btn_fetch_proxies = QPushButton("获取代理")
        self.btn_save_config = QPushButton("保存配置")

        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """
        
        save_style = button_style.replace("#007bff", "#28a745").replace("#0056b3", "#218838")
        
        self.btn_test_api.setStyleSheet(button_style)
        self.btn_fetch_proxies.setStyleSheet(button_style)
        self.btn_save_config.setStyleSheet(save_style)

        button_layout.addWidget(self.btn_test_api)
        button_layout.addWidget(self.btn_fetch_proxies)
        button_layout.addWidget(self.btn_save_config)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 状态信息
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)

        self.lbl_api_status = QLabel("API状态: 未测试")
        self.lbl_proxy_status = QLabel("代理状态: 无")
        
        status_layout.addWidget(self.lbl_api_status)
        status_layout.addWidget(self.lbl_proxy_status)

        layout.addWidget(status_group)

        # 代理列表（可选显示）
        self.proxy_group = QGroupBox("获取的代理列表")
        proxy_layout = QVBoxLayout(self.proxy_group)

        self.te_proxies = QTextEdit()
        self.te_proxies.setReadOnly(True)
        self.te_proxies.setFont(QFont("Consolas", 9))
        self.te_proxies.setMaximumHeight(120)
        self.te_proxies.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """)
        proxy_layout.addWidget(self.te_proxies)

        layout.addWidget(self.proxy_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 连接信号
        self.connect_signals()

    def connect_signals(self):
        """连接信号槽"""
        self.btn_test_api.clicked.connect(self.test_api)
        self.btn_fetch_proxies.clicked.connect(self.fetch_proxies)
        self.btn_save_config.clicked.connect(self.save_config)
        
        # 配置变化时发送信号
        self.cb_enable_proxy.toggled.connect(self.on_config_changed)
        self.le_secret_id.textChanged.connect(self.on_config_changed)
        self.le_signature.textChanged.connect(self.on_config_changed)

    def load_config(self):
        """加载配置"""
        try:
            if self.config.is_valid():
                self.cb_enable_proxy.setChecked(True)
                self.le_secret_id.setText(self.config.secret_id)
                self.le_signature.setText(self.config.signature)
                self.lbl_api_status.setText("API状态: ✅ 已配置")
            else:
                self.cb_enable_proxy.setChecked(False)
                self.lbl_api_status.setText("API状态: ❌ 未配置")
        except ConfigException:
            self.cb_enable_proxy.setChecked(False)
            self.lbl_api_status.setText("API状态: ❌ 未配置")

    def save_config(self):
        """保存配置"""
        enabled = self.cb_enable_proxy.isChecked()
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()

        if enabled and (not secret_id or not signature):
            QMessageBox.warning(
                self, "配置不完整",
                "❌ 启用代理但未配置API凭证！\n请填写Secret ID和Signature。"
            )
            return False

        try:
            # 更新配置
            if enabled and secret_id and signature:
                self.config = KuaidailiConfig(secret_id=secret_id, signature=signature)
                self.lbl_api_status.setText("API状态: ✅ 已配置")
                self.status_changed.emit("配置已保存")
            else:
                self.config = KuaidailiConfig()
                self.lbl_api_status.setText("API状态: ❌ 未配置")
                self.status_changed.emit("代理已禁用")

            # 发送配置变化信号
            self.config_changed.emit(self.config)
            return True

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置时发生错误：{str(e)}")
            return False

    def test_api(self):
        """测试API连接"""
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()
        
        if not secret_id or not signature:
            QMessageBox.warning(self, "输入错误", "请先输入Secret ID和Signature！")
            return
        
        try:
            # 创建临时客户端进行测试
            temp_config = KuaidailiConfig(secret_id=secret_id, signature=signature)
            temp_client = KuaidailiClient(config=temp_config)
            
            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            
            # 测试连接
            success, message = temp_client.test_connection()
            
            self.progress_bar.setVisible(False)
            
            if success:
                self.lbl_api_status.setText("API状态: ✅ 连接成功")
                self.status_changed.emit("API连接成功")
                QMessageBox.information(self, "测试成功", f"快代理API连接成功！\n{message}")
            else:
                self.lbl_api_status.setText("API状态: ❌ 连接失败")
                self.status_changed.emit("API连接失败")
                QMessageBox.warning(self, "测试失败", f"快代理API连接失败！\n{message}")
                
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.lbl_api_status.setText("API状态: ❌ 测试异常")
            self.status_changed.emit("API测试异常")
            QMessageBox.critical(self, "测试异常", f"测试过程中发生错误：{str(e)}")

    def fetch_proxies(self):
        """获取代理"""
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()
        
        if not secret_id or not signature:
            QMessageBox.warning(self, "输入错误", "请先输入Secret ID和Signature！")
            return
        
        try:
            # 创建临时客户端
            temp_config = KuaidailiConfig(secret_id=secret_id, signature=signature)
            temp_client = KuaidailiClient(config=temp_config)
            
            proxy_count = self.sb_proxy_count.value()
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            
            # 获取代理
            proxies = temp_client.get_proxy_list(num=proxy_count, validate=False)
            
            self.progress_bar.setVisible(False)
            
            if proxies:
                proxy_text = '\n'.join(proxies)
                self.te_proxies.setPlainText(proxy_text)
                self.lbl_proxy_status.setText(f"代理状态: ✅ 获取 {len(proxies)} 个")
                self.status_changed.emit(f"成功获取 {len(proxies)} 个代理")
            else:
                self.lbl_proxy_status.setText("代理状态: ❌ 获取失败")
                self.status_changed.emit("代理获取失败")
                QMessageBox.warning(self, "获取失败", "无法获取代理，请检查API配置和网络连接！")
                
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.lbl_proxy_status.setText("代理状态: ❌ 获取异常")
            self.status_changed.emit("代理获取异常")
            QMessageBox.critical(self, "获取异常", f"获取代理时发生错误：{str(e)}")

    def on_config_changed(self):
        """配置变化处理"""
        # 可以在这里添加实时验证逻辑
        pass

    def get_config(self):
        """获取当前配置"""
        return self.config

    def set_proxy_list_visible(self, visible):
        """设置代理列表是否可见"""
        self.proxy_group.setVisible(visible)

    def clear_proxy_list(self):
        """清空代理列表"""
        self.te_proxies.clear()
        self.lbl_proxy_status.setText("代理状态: 已清空")
