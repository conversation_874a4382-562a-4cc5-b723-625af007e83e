"""
GUI样式定义模块
定义Windows11风格的界面样式
"""

# 字体堆栈
PREFERRED_FONTS = ["Segoe UI Variable", "Segoe UI", "Helvetica Neue", "Arial", "sans-serif"]

# 颜色定义
COLORS = {
    # Windows 11 主题色
    'primary': '#0078D4',
    'primary_hover': '#106EBE',
    'primary_pressed': '#005A9E',
    
    # 背景色
    'background': '#F3F3F3',
    'surface': '#FFFFFF',
    'surface_hover': '#F9F9F9',
    'surface_pressed': '#F0F0F0',
    
    # 文本色
    'text_primary': '#323130',
    'text_secondary': '#605E5C',
    'text_disabled': '#A19F9D',
    'text_on_primary': '#FFFFFF',
    
    # 边框色
    'border': '#E1DFDD',
    'border_hover': '#C7C6C4',
    'border_focus': '#0078D4',
    
    # 状态色
    'success': '#107C10',
    'warning': '#FF8C00',
    'error': '#D13438',
    'info': '#0078D4',
    
    # 日志颜色
    'log_info': '#323130',
    'log_success': '#107C10',
    'log_warning': '#FF8C00',
    'log_error': '#D13438',
    'log_debug': '#605E5C',
}

# 基础样式
BASE_STYLE = f"""
QWidget {{
    font-family: "{PREFERRED_FONTS[0]}", "{PREFERRED_FONTS[1]}", "{PREFERRED_FONTS[2]}";
    font-size: 9pt;
    color: {COLORS['text_primary']};
    background-color: {COLORS['background']};
}}

QMainWindow {{
    background-color: {COLORS['background']};
}}
"""

# 按钮样式
BUTTON_STYLE = f"""
QPushButton {{
    background-color: {COLORS['primary']};
    color: {COLORS['text_on_primary']};
    border: 1px solid {COLORS['primary']};
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 600;
    min-width: 80px;
    min-height: 32px;
}}

QPushButton:hover {{
    background-color: {COLORS['primary_hover']};
    border-color: {COLORS['primary_hover']};
}}

QPushButton:pressed {{
    background-color: {COLORS['primary_pressed']};
    border-color: {COLORS['primary_pressed']};
}}

QPushButton:disabled {{
    background-color: {COLORS['surface_pressed']};
    color: {COLORS['text_disabled']};
    border-color: {COLORS['border']};
}}

QPushButton.secondary {{
    background-color: {COLORS['surface']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border']};
}}

QPushButton.secondary:hover {{
    background-color: {COLORS['surface_hover']};
    border-color: {COLORS['border_hover']};
}}

QPushButton.secondary:pressed {{
    background-color: {COLORS['surface_pressed']};
}}

QPushButton.danger {{
    background-color: {COLORS['error']};
    border-color: {COLORS['error']};
}}

QPushButton.success {{
    background-color: {COLORS['success']};
    border-color: {COLORS['success']};
}}
"""

# 输入框样式
INPUT_STYLE = f"""
QLineEdit, QTextEdit, QPlainTextEdit {{
    background-color: {COLORS['surface']};
    border: 1px solid {COLORS['border']};
    border-radius: 4px;
    padding: 8px 12px;
    selection-background-color: {COLORS['primary']};
    selection-color: {COLORS['text_on_primary']};
    text-align: left;
}}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
    border-color: {COLORS['border_focus']};
    outline: none;
}}

QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {{
    border-color: {COLORS['border_hover']};
}}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {{
    background-color: {COLORS['surface_pressed']};
    color: {COLORS['text_disabled']};
}}
"""

# 标签样式
LABEL_STYLE = f"""
QLabel {{
    color: {COLORS['text_primary']};
    background-color: transparent;
}}

QLabel.title {{
    font-size: 14pt;
    font-weight: 600;
    color: {COLORS['text_primary']};
}}

QLabel.subtitle {{
    font-size: 11pt;
    font-weight: 500;
    color: {COLORS['text_secondary']};
}}

QLabel.caption {{
    font-size: 8pt;
    color: {COLORS['text_secondary']};
}}

QLabel.error {{
    color: {COLORS['error']};
}}

QLabel.success {{
    color: {COLORS['success']};
}}

QLabel.warning {{
    color: {COLORS['warning']};
}}
"""

# 分组框样式
GROUPBOX_STYLE = f"""
QGroupBox {{
    font-weight: 600;
    border: 1px solid {COLORS['border']};
    border-radius: 6px;
    margin-top: 8px;
    padding-top: 16px;
    background-color: {COLORS['surface']};
}}

QGroupBox::title {{
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px 0 8px;
    color: {COLORS['text_primary']};
    background-color: {COLORS['surface']};
}}
"""

# 进度条样式
PROGRESSBAR_STYLE = f"""
QProgressBar {{
    border: 1px solid {COLORS['border']};
    border-radius: 4px;
    text-align: center;
    background-color: {COLORS['surface']};
    color: {COLORS['text_primary']};
    font-weight: 500;
}}

QProgressBar::chunk {{
    background-color: {COLORS['primary']};
    border-radius: 3px;
}}
"""

# 状态栏样式
STATUSBAR_STYLE = f"""
QStatusBar {{
    background-color: {COLORS['surface']};
    border-top: 1px solid {COLORS['border']};
    color: {COLORS['text_secondary']};
    font-size: 8pt;
}}

QStatusBar::item {{
    border: none;
}}
"""

# 菜单栏样式
MENUBAR_STYLE = f"""
QMenuBar {{
    background-color: {COLORS['surface']};
    border-bottom: 1px solid {COLORS['border']};
    color: {COLORS['text_primary']};
    padding: 4px;
}}

QMenuBar::item {{
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}}

QMenuBar::item:selected {{
    background-color: {COLORS['surface_hover']};
}}

QMenuBar::item:pressed {{
    background-color: {COLORS['surface_pressed']};
}}

QMenu {{
    background-color: {COLORS['surface']};
    border: 1px solid {COLORS['border']};
    border-radius: 6px;
    padding: 4px;
    color: {COLORS['text_primary']};
}}

QMenu::item {{
    padding: 8px 16px;
    border-radius: 4px;
}}

QMenu::item:selected {{
    background-color: {COLORS['surface_hover']};
}}

QMenu::separator {{
    height: 1px;
    background-color: {COLORS['border']};
    margin: 4px 8px;
}}
"""

# 滚动条样式
SCROLLBAR_STYLE = f"""
QScrollBar:vertical {{
    background-color: {COLORS['surface']};
    width: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:vertical {{
    background-color: {COLORS['border_hover']};
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}}

QScrollBar::handle:vertical:hover {{
    background-color: {COLORS['text_secondary']};
}}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
    height: 0px;
}}

QScrollBar:horizontal {{
    background-color: {COLORS['surface']};
    height: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:horizontal {{
    background-color: {COLORS['border_hover']};
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}}

QScrollBar::handle:horizontal:hover {{
    background-color: {COLORS['text_secondary']};
}}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
    width: 0px;
}}
"""

# 选项卡样式
TAB_STYLE = f"""
QTabWidget::pane {{
    border: 1px solid {COLORS['border']};
    border-radius: 6px;
    background-color: {COLORS['surface']};
    margin-top: -1px;
}}

QTabBar::tab {{
    background-color: {COLORS['surface']};
    border: 1px solid {COLORS['border']};
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-bottom: none;
}}

QTabBar::tab:selected {{
    background-color: {COLORS['surface']};
    border-bottom: 2px solid {COLORS['primary']};
}}

QTabBar::tab:hover {{
    background-color: {COLORS['surface_hover']};
}}
"""

# 复选框和单选框样式
CHECKBOX_STYLE = f"""
QCheckBox, QRadioButton {{
    color: {COLORS['text_primary']};
    spacing: 8px;
}}

QCheckBox::indicator, QRadioButton::indicator {{
    width: 16px;
    height: 16px;
    border: 1px solid {COLORS['border']};
    background-color: {COLORS['surface']};
}}

QCheckBox::indicator {{
    border-radius: 3px;
}}

QRadioButton::indicator {{
    border-radius: 8px;
}}

QCheckBox::indicator:checked, QRadioButton::indicator:checked {{
    background-color: {COLORS['primary']};
    border-color: {COLORS['primary']};
}}

QCheckBox::indicator:hover, QRadioButton::indicator:hover {{
    border-color: {COLORS['border_hover']};
}}
"""

# 组合所有样式
COMPLETE_STYLE = f"""
{BASE_STYLE}
{BUTTON_STYLE}
{INPUT_STYLE}
{LABEL_STYLE}
{GROUPBOX_STYLE}
{PROGRESSBAR_STYLE}
{STATUSBAR_STYLE}
{MENUBAR_STYLE}
{SCROLLBAR_STYLE}
{TAB_STYLE}
{CHECKBOX_STYLE}
"""

def get_complete_stylesheet() -> str:
    """获取完整的样式表"""
    return COMPLETE_STYLE

def get_log_color(level: str) -> str:
    """获取日志级别对应的颜色"""
    level_colors = {
        'INFO': COLORS['log_info'],
        'SUCCESS': COLORS['log_success'],
        'WARNING': COLORS['log_warning'],
        'ERROR': COLORS['log_error'],
        'DEBUG': COLORS['log_debug']
    }
    return level_colors.get(level.upper(), COLORS['log_info'])
