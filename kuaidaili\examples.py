"""
快代理SDK使用示例
"""
import os
import time
from kuaidaili_sdk import KuaidailiClient, ProxyPool, KuaidailiConfig


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建客户端
    client = KuaidailiClient(
        secret_id="your_secret_id",
        signature="your_signature"
    )
    
    # 测试连接
    success, message = client.test_connection()
    print(f"连接测试: {message}")
    
    if success:
        # 获取单个代理
        proxy = client.get_proxy()
        print(f"获取到代理: {proxy}")
        
        # 获取验证过的代理
        verified_proxy = client.get_verified_proxy()
        print(f"验证通过的代理: {verified_proxy}")
        
        # 获取代理列表
        proxy_list = client.get_proxy_list(num=5, validate=True)
        print(f"获取到 {len(proxy_list)} 个可用代理")


def example_config_file():
    """配置文件使用示例"""
    print("\n=== 配置文件使用示例 ===")
    
    # 创建示例配置文件
    config_content = """[<PERSON><PERSON><PERSON><PERSON>]
secret_id = your_secret_id
signature = your_signature
"""
    
    with open("example_config.ini", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    try:
        # 从配置文件创建客户端
        client = KuaidailiClient.from_config_file("example_config.ini")
        print("从配置文件创建客户端成功")
        
        # 测试连接
        success, message = client.test_connection()
        print(f"连接测试: {message}")
        
    except Exception as e:
        print(f"配置文件示例失败: {e}")
    finally:
        # 清理示例文件
        if os.path.exists("example_config.ini"):
            os.remove("example_config.ini")


def example_environment_variables():
    """环境变量使用示例"""
    print("\n=== 环境变量使用示例 ===")
    
    # 设置环境变量
    os.environ['KUAIDAILI_SECRET_ID'] = 'your_secret_id'
    os.environ['KUAIDAILI_SIGNATURE'] = 'your_signature'
    
    try:
        # 从环境变量创建客户端
        client = KuaidailiClient.from_env()
        print("从环境变量创建客户端成功")
        
        # 测试连接
        success, message = client.test_connection()
        print(f"连接测试: {message}")
        
    except Exception as e:
        print(f"环境变量示例失败: {e}")


def example_proxy_pool():
    """代理池使用示例"""
    print("\n=== 代理池使用示例 ===")
    
    try:
        # 创建客户端
        client = KuaidailiClient(
            secret_id="your_secret_id",
            signature="your_signature"
        )
        
        # 创建代理池
        proxy_pool = ProxyPool(
            client=client,
            pool_size=10,        # 代理池大小
            auto_refresh=True,   # 自动刷新
            refresh_interval=300 # 刷新间隔(秒)
        )
        
        # 从代理池获取代理
        for i in range(3):
            proxy = proxy_pool.get_proxy()
            print(f"第 {i+1} 次获取代理: {proxy}")
            time.sleep(1)
        
        # 查看代理池状态
        status = proxy_pool.get_pool_status()
        print(f"代理池状态: {status}")
        
    except Exception as e:
        print(f"代理池示例失败: {e}")


def example_proxy_session():
    """代理会话使用示例"""
    print("\n=== 代理会话使用示例 ===")
    
    try:
        # 创建客户端和代理池
        client = KuaidailiClient(
            secret_id="your_secret_id",
            signature="your_signature"
        )
        proxy_pool = ProxyPool(client, pool_size=5)
        
        # 使用代理会话发送请求
        with proxy_pool.get_session() as session:
            # 测试获取IP
            response = session.get("http://httpbin.org/ip", timeout=10)
            if response.status_code == 200:
                print(f"当前IP信息: {response.json()}")
            else:
                print(f"请求失败: {response.status_code}")
                
    except Exception as e:
        print(f"代理会话示例失败: {e}")


def example_proxy_validation():
    """代理验证示例"""
    print("\n=== 代理验证示例 ===")
    
    try:
        # 创建客户端
        client = KuaidailiClient(
            secret_id="your_secret_id",
            signature="your_signature"
        )
        
        # 获取一些代理进行验证
        proxies = client.get_proxy_list(num=3, validate=False)
        print(f"获取到 {len(proxies)} 个代理，开始验证...")
        
        # 逐个验证代理
        for i, proxy in enumerate(proxies, 1):
            is_valid = client.validate_proxy(proxy)
            status = "✅ 可用" if is_valid else "❌ 不可用"
            print(f"代理 {i}: {proxy} - {status}")
            
    except Exception as e:
        print(f"代理验证示例失败: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    from kuaidaili_sdk.exceptions import APIException, ProxyException, ConfigException
    
    try:
        # 使用错误的凭证
        client = KuaidailiClient(
            secret_id="wrong_id",
            signature="wrong_signature"
        )
        
        # 尝试获取代理
        proxy = client.get_proxy()
        
    except APIException as e:
        print(f"API异常: {e}")
    except ProxyException as e:
        print(f"代理异常: {e}")
    except ConfigException as e:
        print(f"配置异常: {e}")
    except Exception as e:
        print(f"其他异常: {e}")


def example_advanced_config():
    """高级配置示例"""
    print("\n=== 高级配置示例 ===")
    
    try:
        # 使用配置对象
        config = KuaidailiConfig(
            secret_id="your_secret_id",
            signature="your_signature"
        )
        
        # 创建客户端
        client = KuaidailiClient(config=config, timeout=15)
        
        # 创建代理池，自定义参数
        proxy_pool = ProxyPool(
            client=client,
            pool_size=20,           # 更大的代理池
            auto_refresh=True,      # 自动刷新
            refresh_interval=600,   # 10分钟刷新一次
            max_failures=5          # 允许更多失败次数
        )
        
        print("高级配置创建成功")
        
        # 获取状态信息
        status = proxy_pool.get_pool_status()
        print(f"代理池状态: {status}")
        
    except Exception as e:
        print(f"高级配置示例失败: {e}")


if __name__ == "__main__":
    """运行所有示例"""
    print("快代理SDK使用示例")
    print("=" * 50)
    
    # 注意：运行前请替换为真实的API凭证
    print("⚠️  请先替换示例中的API凭证为真实凭证")
    print()
    
    # 运行各种示例
    example_basic_usage()
    example_config_file()
    example_environment_variables()
    example_proxy_pool()
    example_proxy_session()
    example_proxy_validation()
    example_error_handling()
    example_advanced_config()
    
    print("\n" + "=" * 50)
    print("所有示例运行完成")
