"""
全新的日志管理模块 - 修复显示问题
"""
import os
import logging
import time
from datetime import datetime
from typing import Optional, Callable, List
from PyQt5.QtWidgets import QTextEdit
from PyQt5.QtGui import QFont, QTextCursor
from PyQt5.QtCore import QTimer, pyqtSignal, QObject


class SimpleGUILogHandler:
    """超简单的GUI日志处理器"""

    def __init__(self, max_lines: int = 200):
        self.text_widget = None
        self.max_lines = max_lines

    def set_text_widget(self, text_widget: QTextEdit):
        """设置文本控件"""
        self.text_widget = text_widget
        if self.text_widget:
            # 设置为纯文本模式
            self.text_widget.setAcceptRichText(False)
            # 设置等宽字体
            font = QFont("Consolas", 9)
            self.text_widget.setFont(font)

    def append_log(self, message: str, level: str = "INFO"):
        """添加日志消息 - 最简单的实现"""
        if not self.text_widget:
            return

        try:
            # 清理消息
            if isinstance(message, bytes):
                message = message.decode('utf-8', errors='replace')

            # 移除问题字符
            message = message.replace('\r', '').strip()

            # 限制长度
            if len(message) > 200:
                message = message[:197] + "..."

            # 添加时间戳
            timestamp = datetime.now().strftime("[%H:%M:%S]")
            full_message = f"{timestamp} {message}"

            # 检查行数，超过限制就清空
            current_text = self.text_widget.toPlainText()
            lines = current_text.split('\n') if current_text else []

            if len(lines) >= self.max_lines:
                # 保留最后一半的行
                keep_lines = lines[-(self.max_lines//2):]
                self.text_widget.setPlainText('\n'.join(keep_lines))

            # 添加新行
            if current_text:
                self.text_widget.append(full_message)
            else:
                self.text_widget.setPlainText(full_message)

            # 滚动到底部
            cursor = self.text_widget.textCursor()
            cursor.movePosition(QTextCursor.End)
            self.text_widget.setTextCursor(cursor)

        except Exception as e:
            print(f"日志添加失败: {e}")

    def clear_logs(self):
        """清空日志"""
        try:
            if self.text_widget:
                self.text_widget.clear()
        except Exception as e:
            print(f"清空日志失败: {e}")

    def get_log_count(self):
        """获取日志行数"""
        try:
            if self.text_widget:
                text = self.text_widget.toPlainText()
                return len(text.split('\n')) if text else 0
            return 0
        except:
            return 0


class SimpleLogger:
    """简化的日志记录器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.gui_callback = None
        self.file_logger = None
        
        # 创建日志目录
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置文件日志
        self._setup_file_logger()
    
    def _setup_file_logger(self):
        """设置文件日志"""
        try:
            log_file = os.path.join(self.log_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")
            
            # 创建文件日志记录器
            self.file_logger = logging.getLogger('app_logger')
            self.file_logger.setLevel(logging.INFO)
            
            # 清除现有处理器
            for handler in self.file_logger.handlers[:]:
                self.file_logger.removeHandler(handler)
            
            # 添加文件处理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            self.file_logger.addHandler(file_handler)
            
        except Exception as e:
            print(f"设置文件日志失败: {e}")
    
    def set_gui_callback(self, callback: Callable):
        """设置GUI回调"""
        self.gui_callback = callback
    
    def _log(self, message: str, level: str = "INFO"):
        """内部日志方法"""
        try:
            # 文件日志
            if self.file_logger:
                if level == "ERROR":
                    self.file_logger.error(message)
                elif level == "WARNING":
                    self.file_logger.warning(message)
                else:
                    self.file_logger.info(message)
            
            # GUI日志
            if self.gui_callback:
                self.gui_callback(message, level)
            
            # 控制台日志
            print(f"[{level}] {message}")
            
        except Exception as e:
            print(f"日志记录失败: {e}")
    
    def info(self, message: str):
        """信息日志"""
        self._log(message, "INFO")
    
    def success(self, message: str):
        """成功日志"""
        self._log(message, "SUCCESS")
    
    def warning(self, message: str):
        """警告日志"""
        self._log(message, "WARNING")
    
    def error(self, message: str):
        """错误日志"""
        self._log(message, "ERROR")
    
    def debug(self, message: str):
        """调试日志"""
        self._log(message, "DEBUG")
    
    def log(self, message: str, level: str = "INFO"):
        """通用日志方法"""
        self._log(message, level)


# 全局实例
_global_logger = None
_global_gui_handler = None

def get_logger() -> SimpleLogger:
    """获取全局日志实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = SimpleLogger()
    return _global_logger

def get_gui_handler() -> SimpleGUILogHandler:
    """获取全局GUI处理器"""
    global _global_gui_handler
    if _global_gui_handler is None:
        _global_gui_handler = SimpleGUILogHandler()
    return _global_gui_handler

def setup_logger(log_dir: str = "logs") -> SimpleLogger:
    """设置全局日志实例"""
    global _global_logger
    _global_logger = SimpleLogger(log_dir)
    return _global_logger
