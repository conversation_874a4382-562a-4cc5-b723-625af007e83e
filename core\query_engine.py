"""
查询引擎模块
基于原query.py的核心逻辑，提供批量查询功能
"""
import requests
import re
import json
import time
import random
import os
from typing import List, Dict, Optional, Callable


class QueryEngine:
    """查询引擎类"""
    
    def __init__(self, proxy_manager=None, logger=None):
        self.proxy_manager = proxy_manager
        self.logger = logger
        self.log_callback = None  # 线程安全的日志回调
        self.headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/x-www-form-urlencoded',
            'DNT': '1',
            'Origin': 'http://search.gjsy.gov.cn',
            'Pragma': 'no-cache',
            'Proxy-Connection': 'keep-alive',
            'Referer': 'http://search.gjsy.gov.cn/index.jsp?t=12&c=440104&at=10&sn=1&mc=20',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        }
        self.is_running = False
        self.query_count = 0
        self.proxy_switch_interval = random.randint(8, 12)  # 固定的切换间隔

    def set_log_callback(self, callback: Callable):
        """设置线程安全的日志回调"""
        self.log_callback = callback
        
    def log(self, message: str, level: str = "INFO"):
        """记录日志 - 线程安全版本"""
        # 只使用线程安全回调或控制台输出
        if self.log_callback:
            try:
                self.log_callback(message, level)
                return
            except Exception as e:
                print(f"日志回调失败: {e}")

        # 如果没有回调，只输出到控制台，绝不调用GUI
        print(f"[{level}] {message}")
    
    def load_region_codes(self, file_path: str = None) -> List[Dict]:
        """加载地区代码和名称（使用内置数据）"""
        try:
            from core.region_data import get_region_codes
            return get_region_codes()
        except Exception as e:
            self.log(f"加载内置地区代码时出错: {e}", "ERROR")
            return []
    
    def load_existing_data(self, file_path: str = "extracted_data.json") -> List[Dict]:
        """加载已存在的JSON数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:  # 文件为空
                    return []
                return json.loads(content)
        except FileNotFoundError:
            return []
        except json.JSONDecodeError:
            self.log(f"{file_path}文件格式错误，将重新创建", "WARNING")
            return []
        except Exception as e:
            self.log(f"读取{file_path}时出错: {e}", "ERROR")
            return []
    
    def filter_new_data(self, school_data: List[Dict], existing_data: List[Dict]) -> List[Dict]:
        """筛选出真正的新增数据（通过统一社会信用代码对比）"""
        if not school_data:
            return []

        # 提取已存在数据的统一社会信用代码集合
        existing_codes = set()
        # 同时建立一个用于比较缺失代码记录的集合（基于单位名称+地区+通知时间）
        existing_records_without_code = set()

        for item in existing_data:
            code = item.get('统一社会信用代码', '')
            if code:
                existing_codes.add(code)
            else:
                # 对于缺失统一社会信用代码的记录，使用单位名称+地区+通知时间作为唯一标识
                unit_name = item.get('单位名称', '')
                region_name = item.get('地区名称', '')
                notify_time = item.get('通知时间', '')
                if unit_name:  # 至少要有单位名称
                    record_key = f"{unit_name}|{region_name}|{notify_time}"
                    existing_records_without_code.add(record_key)

        # 筛选新增数据
        new_data = []
        for item in school_data:
            code = item.get('统一社会信用代码', '')

            if code:
                # 有统一社会信用代码的记录，按代码去重
                if code not in existing_codes:
                    new_data.append(item)
                    # 添加到已存在代码集合中，避免同一批次内的重复
                    existing_codes.add(code)
                    self.log(f"新增记录（有代码）: {item.get('单位名称', '')} - {code}")
            else:
                # 缺失统一社会信用代码的记录，使用组合键去重
                unit_name = item.get('单位名称', '')
                region_name = item.get('地区名称', '')
                notify_time = item.get('通知时间', '')

                if unit_name:  # 至少要有单位名称才保存
                    record_key = f"{unit_name}|{region_name}|{notify_time}"
                    if record_key not in existing_records_without_code:
                        new_data.append(item)
                        existing_records_without_code.add(record_key)
                        self.log(f"新增记录（无代码）: {unit_name} - 统一社会信用代码缺失", "WARNING")
                else:
                    self.log(f"跳过无效记录：单位名称和统一社会信用代码均缺失", "WARNING")

        return new_data

    def save_data_to_json(self, new_data: List[Dict], existing_data: List[Dict],
                         file_path: str = "extracted_data.json") -> List[Dict]:
        """将新数据追加到现有数据并保存到JSON文件"""
        try:
            # 合并数据
            all_data = existing_data + new_data

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)

            # 验证保存是否成功
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)
                if len(saved_data) != len(all_data):
                    self.log(f"❌ 数据保存验证失败：期望 {len(all_data)} 条，实际 {len(saved_data)} 条", "ERROR")
            except Exception as verify_e:
                self.log(f"❌ 数据保存验证失败: {verify_e}", "ERROR")

            return all_data
        except Exception as e:
            self.log(f"保存数据时出错: {e}", "ERROR")
            return existing_data
    
    def get_application_type(self, type_code: str) -> str:
        """根据申办事项类型代码返回对应的中文名称"""
        type_mapping = {
            "0": "设立登记",
            "1": "变更登记",
            "3": "注销登记",
            "4": "证书补领",
            "5": "重新申领证书"
        }
        return type_mapping.get(type_code, "未知类型")
    
    def extract_data_from_html(self, html_content: str) -> List[Dict]:
        """从HTML内容中提取申办事项数据"""
        try:
            # 使用正则表达式提取JavaScript数组中的数据
            # 匹配 d.push("值"); 的模式
            pattern = r'd\.push\("([^"]*)"\);'
            matches = re.findall(pattern, html_content)

            # 每6个元素为一组数据
            data_list = []
            for i in range(0, len(matches), 6):
                try:
                    if i + 5 < len(matches):
                        item = {
                            "申办事项": self.get_application_type(matches[i]),
                            "单位名称": matches[i + 1],
                            "统一社会信用代码": matches[i + 2],
                            # 跳过状态字段 matches[i + 3]
                            "通知时间": matches[i + 4] if matches[i + 4] else matches[i + 5]
                        }
                        data_list.append(item)
                except Exception as e:
                    self.log(f"处理数据项 {i} 时出错: {e}", "WARNING")
                    continue

            return data_list

        except Exception as e:
            self.log(f"❌ HTML解析失败: {e}", "ERROR")
            import traceback
            self.log(f"解析错误详情: {traceback.format_exc()}", "ERROR")
            return []
    
    def query_region_data(self, region_code: str, region_name: str,
                         max_retries: int = 3) -> List[Dict]:
        """查询指定地区的数据，带重试机制"""
        # 记录已尝试过的代理，避免重复使用失效代理
        tried_proxies = set()

        for attempt in range(max_retries):
            try:
                # 生成当前时间戳（毫秒级）
                current_timestamp = str(int(time.time() * 1000))

                data = {
                    'c': region_code,
                    't': '12',
                    'at': '0',  # 设立登记
                    'sn': '1',
                    'mc': '20',
                    'ts': current_timestamp,
                    'w': 'null',
                    'atRadio': '0',
                    'h': 'null'
                }

                self.log(f"正在查询 {region_name} ({region_code})，第 {attempt + 1} 次尝试...")

                # 获取代理（每次尝试都获取新代理）
                proxies = None
                if self.proxy_manager:
                    # 如果不是第一次尝试，强制切换代理
                    if attempt > 0:
                        self.log(f"🔄 上次查询失败，切换到新代理...")
                        self.proxy_manager.switch_proxy()

                    proxies = self.proxy_manager.get_proxy()

                    # 检查是否已经尝试过这个代理
                    if proxies:
                        proxy_str = proxies.get('http', '')
                        if proxy_str in tried_proxies:
                            self.log(f"⚠️ 已尝试过此代理，再次切换...")
                            self.proxy_manager.switch_proxy()
                            proxies = self.proxy_manager.get_proxy()

                        if proxies:
                            # 记录已尝试的代理
                            tried_proxies.add(proxies.get('http', ''))
                            self.log(f"🌐 使用代理: {proxies.get('http', '直连')}")
                        else:
                            self.log("❌ 无法获取有效代理，程序必须使用代理才能运行！")
                            raise Exception("程序必须使用代理才能运行，请配置快代理API")
                    else:
                        self.log("❌ 无法获取有效代理，程序必须使用代理才能运行！")
                        raise Exception("程序必须使用代理才能运行，请配置快代理API")
                else:
                    self.log("❌ 代理管理器未初始化，程序必须使用代理才能运行！")
                    raise Exception("程序必须使用代理才能运行，请配置快代理API")
                
                try:
                    response = requests.post('http://search.gjsy.gov.cn/bjtz/query',
                                           headers=self.headers, data=data,
                                           verify=False, timeout=10, proxies=proxies)
                except Exception as req_e:
                    self.log(f"❌ 请求异常: {req_e}", "ERROR")
                    raise req_e
                
                if response.status_code == 200:
                    try:
                        # 提取数据
                        extracted_data = self.extract_data_from_html(response.text)

                        # 筛选包含"中学"或"学校"的记录
                        school_data = []
                        for item in extracted_data:
                            try:
                                unit_name = item.get('单位名称', '')
                                if "中学" in unit_name or "学校" in unit_name:
                                    # 添加地区信息
                                    item['地区代码'] = region_code
                                    item['地区名称'] = region_name
                                    school_data.append(item)
                            except Exception as e:
                                self.log(f"处理单条记录时出错: {e}", "WARNING")
                                continue

                        self.log(f"成功获取数据，找到 {len(school_data)} 条学校相关记录")
                        return school_data
                    except Exception as e:
                        self.log(f"数据处理时出错: {e}", "ERROR")
                        return []
                    
            except Exception as e:
                self.log(f"第 {attempt + 1} 次尝试失败: {e}", "WARNING")
                if attempt < max_retries - 1:
                    wait_time = random.uniform(2, 4)  # 失败时等待2-4秒
                    self.log(f"等待 {wait_time:.1f} 秒后重试...")
                    time.sleep(wait_time)
        
        self.log(f"{region_name} ({region_code}) 查询失败，已达到最大重试次数", "ERROR")
        return []
    
    def stop_query(self):
        """停止查询"""
        self.is_running = False
        self.log("查询已停止")
    
    def batch_query(self, region_file: str = "region_code.json",
                   data_file: str = "extracted_data.json",
                   progress_callback: Optional[Callable] = None,
                   result_callback: Optional[Callable] = None) -> Dict:
        """批量查询所有地区"""
        try:
            self.is_running = True
            self.query_count = 0

            # 加载地区代码
            regions = self.load_region_codes(region_file)
            if not regions:
                self.log("无法加载地区代码，查询终止", "ERROR")
                return {"success": False, "message": "无法加载地区代码"}

            # 确保regions至少有一个元素
            if len(regions) == 0:
                self.log("地区代码列表为空，查询终止", "ERROR")
                return {"success": False, "message": "地区代码列表为空"}

            self.log(f"开始查询 {len(regions)} 个地区...")

            # 加载已存在的数据
            existing_data = self.load_existing_data(data_file)

            success_count = 0
            total_new_records = 0

            for i, region in enumerate(regions, 1):
                try:
                    if not self.is_running:
                        self.log("查询被用户停止")
                        break

                    # 安全地获取地区代码和名称
                    region_code = region.get('code', '')
                    region_name = region.get('name', '')

                    if not region_code or not region_name:
                        self.log(f"跳过无效地区数据: {region}", "WARNING")
                        continue

                    self.log(f"[{i}/{len(regions)}] 查询地区: {region_name} ({region_code})")
                except Exception as e:
                    self.log(f"处理地区数据时出错: {e}", "ERROR")
                    continue

                # 更新进度
                if progress_callback:
                    progress_callback(i, len(regions), region_name)

                # 添加分隔线
                if i == 1:  # 第一个地区查询前添加分隔线
                    self.log("-----------------------------------------------------")

                # 查询该地区的数据
                school_data = self.query_region_data(region_code, region_name)

                if school_data:
                    # 筛选出真正的新增数据
                    new_data = self.filter_new_data(school_data, existing_data)

                    if new_data:
                        # 保存新增数据到JSON文件
                        updated_data = self.save_data_to_json(new_data, existing_data, data_file)

                        # 检查保存是否成功（通过比较数据长度）
                        if len(updated_data) == len(existing_data) + len(new_data):
                            # 保存成功，更新existing_data
                            existing_data = updated_data
                            success_count += 1
                            total_new_records += len(new_data)
                            self.log(f"✅ {region_name}: 新增 {len(new_data)} 条记录")

                            # 只有保存成功才发送微信通知
                            if result_callback:
                                result_callback(new_data, region_name)
                        else:
                            # 保存失败，不发送通知
                            self.log(f"❌ {region_name}: 数据保存失败", "ERROR")
                    else:
                        self.log(f"本次获取到 {len(school_data)} 条学校记录，已存在于数据库中，无新增")
                else:
                    self.log(f"本次查询未找到学校记录")

                # 每8-12次查询切换代理
                self.query_count += 1
                if self.proxy_manager and self.query_count % self.proxy_switch_interval == 0:
                    self.proxy_manager.switch_proxy()
                    # 重新生成下一个切换间隔
                    self.proxy_switch_interval = random.randint(8, 12)

                # 成功获取数据后随机延迟1-3秒
                if i < len(regions) and self.is_running:
                    delay_time = random.uniform(1, 3)
                    time.sleep(delay_time)
                    # 添加分隔线（除了最后一个地区）
                    if i < len(regions):
                        self.log("---------------------------------------------------")

            # 显示汇总结果
            self.log("=" * 60)
            self.log("批量查询完成！")

            return {
                "success": True,
                "total_regions": len(regions),
                "success_regions": success_count,
                "new_records": total_new_records,
                "total_records": len(existing_data)
            }
        except Exception as e:
            self.log(f"批量查询过程中发生严重错误: {e}", "ERROR")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}", "ERROR")
            return {
                "success": False,
                "message": f"查询过程中发生错误: {e}",
                "total_regions": 0,
                "success_regions": 0,
                "new_records": 0,
                "total_records": 0
            }
