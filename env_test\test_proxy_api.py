#!/usr/bin/env python3
"""
测试代理API功能
检查快代理API是否能正常工作
"""
import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from kuaidaili.client import KuaidailiClient
from kuaidaili.config import KuaidailiConfig
from kuaidaili.exceptions import APIException, ConfigException

def test_proxy_api():
    """测试代理API"""
    print("=" * 60)
    print("快代理API测试")
    print("=" * 60)
    
    # 测试配置1：旧密码
    old_password = "yzsvr0e4n35tmvz9fu33mjm0pjh26o47"
    username = "ok2ybrbcxu1r4ztfpfvf"
    
    print(f"测试用户名: {username}")
    print(f"测试旧密码: {old_password}")
    
    try:
        print("\n1. 测试旧密码...")
        client = KuaidailiClient(secret_id=username, signature=old_password)
        proxy = client.get_proxy(validate=False)
        print(f"✅ 旧密码可用，获取到代理: {proxy}")
    except Exception as e:
        print(f"❌ 旧密码失败: {e}")
    
    # 请用户提供新密码进行测试
    print("\n" + "=" * 60)
    print("请提供新的api_password进行测试")
    print("=" * 60)
    
    new_password = input("请输入新的api_password: ").strip()
    
    if new_password:
        try:
            print(f"\n2. 测试新密码: {new_password}")
            client = KuaidailiClient(secret_id=username, signature=new_password)
            proxy = client.get_proxy(validate=False)
            print(f"✅ 新密码可用，获取到代理: {proxy}")
            
            # 如果新密码可用，更新配置文件
            print("\n3. 更新配置文件...")
            update_config_file(new_password)
            
        except Exception as e:
            print(f"❌ 新密码失败: {e}")
    else:
        print("未提供新密码，跳过测试")

def update_config_file(new_password):
    """更新配置文件中的密码"""
    import json
    
    config_files = [
        "config/config.json",
        "dist/config/config.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                # 读取配置
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新密码
                if 'proxy' in config_data:
                    config_data['proxy']['api_password'] = new_password
                    
                    # 保存配置
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 已更新 {config_file}")
                else:
                    print(f"⚠️ {config_file} 中没有proxy配置段")
                    
            except Exception as e:
                print(f"❌ 更新 {config_file} 失败: {e}")
        else:
            print(f"⚠️ {config_file} 不存在")

def test_current_config():
    """测试当前配置"""
    print("\n" + "=" * 60)
    print("测试当前配置文件中的密码")
    print("=" * 60)
    
    try:
        from config.settings import Settings
        settings = Settings()
        
        username = settings.get('proxy.api_username', '')
        password = settings.get('proxy.api_password', '')
        
        print(f"配置文件中的用户名: {username}")
        print(f"配置文件中的密码: {password}")
        
        if username and password:
            try:
                client = KuaidailiClient(secret_id=username, signature=password)
                proxy = client.get_proxy(validate=False)
                print(f"✅ 当前配置可用，获取到代理: {proxy}")
            except Exception as e:
                print(f"❌ 当前配置失败: {e}")
        else:
            print("❌ 配置不完整")
            
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")

if __name__ == "__main__":
    test_current_config()
    test_proxy_api()
