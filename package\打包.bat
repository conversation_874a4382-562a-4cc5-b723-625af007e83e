@echo off
chcp 65001 >nul
title 登记设立批量查询 - 打包工具

echo ========================================
echo 登记设立批量查询 - 打包工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo 正在安装依赖包...
pip install -r requirements.txt

echo.
echo 开始打包程序...
python build.py

echo.
echo 打包完成！
echo 输出目录: ..\dist\
echo.
pause
