"""
主窗口模块
应用程序的主界面
"""
import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QTextEdit, QProgressBar,
                            QStatusBar, QGroupBox, QFormLayout, QCheckBox,
                            QMessageBox, QSystemTrayIcon, QMenu,
                            QApplication, QFrame, QGridLayout, QFileDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize, QTime
from PyQt5.QtWidgets import QTimeEdit
from PyQt5.QtGui import QIcon, QFont, QPixmap, QTextCursor, QTextOption

from config.settings import get_settings
from gui.styles import get_complete_stylesheet, get_log_color
from utils.logger import get_logger, SimpleGUILogHandler as get_gui_handler
from utils.usage_limiter import UsageLimiter, check_usage_limit, record_query_usage, show_usage_status
from core.query_engine import QueryEngine
from core.proxy_manager import create_proxy_manager
from core.wechat_notifier import WeChatNotifier
from core.data_manager import DataManager
from core.region_data import get_region_codes


class QueryThread(QThread):
    """查询线程"""

    progress_updated = pyqtSignal(int, int, str)  # 当前进度, 总数, 地区名称
    query_finished = pyqtSignal(dict)  # 查询完成
    new_data_found = pyqtSignal(list, str)  # 新数据发现, 地区名称
    log_message = pyqtSignal(str, str)  # 日志消息, 级别 - 线程安全日志
    
    def __init__(self, query_engine):
        super().__init__()
        self.query_engine = query_engine
        
    def run(self):
        """运行查询"""
        try:
            # 强制清除查询引擎的logger引用，确保线程安全
            self.query_engine.logger = None

            # 设置线程安全的日志回调
            self.query_engine.set_log_callback(self.thread_safe_log)

            # 发送开始信号
            self.log_message.emit("开始批量查询...", "INFO")

            # 设置回调函数
            result = self.query_engine.batch_query(
                progress_callback=self.on_progress,
                result_callback=self.on_new_data
            )
            self.query_finished.emit(result)
        except Exception as e:
            # 记录详细错误信息
            import traceback
            error_msg = f"查询线程异常: {e}\n{traceback.format_exc()}"
            print(error_msg)  # 输出到控制台

            # 通过信号发送错误日志
            self.log_message.emit(f"查询异常: {e}", "ERROR")

            self.query_finished.emit({
                "success": False,
                "error": str(e),
                "message": f"查询过程中发生错误: {e}"
            })

    def thread_safe_log(self, message: str, level: str = "INFO"):
        """线程安全的日志方法"""
        self.log_message.emit(message, level)
    
    def on_progress(self, current, total, region_name):
        """进度回调"""
        self.progress_updated.emit(current, total, region_name)
    
    def on_new_data(self, data, region_name):
        """新数据回调"""
        self.new_data_found.emit(data, region_name)
    
    def stop(self):
        """停止查询"""
        if self.query_engine:
            self.query_engine.stop_query()


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化组件
        self.settings = get_settings()
        self.logger = get_logger()
        self.usage_limiter = UsageLimiter()  # 硬编码每天最多4次查询
        self.data_manager = DataManager(self.logger)
        
        # 查询相关
        self.query_engine = None
        self.query_thread = None
        self.proxy_manager = None
        self.wechat_notifier = None
        self.query_start_total = 0  # 查询开始时的总记录数
        self.query_start_today = 0  # 查询开始时的今日记录数
        
        # GUI组件
        self.log_handler = get_gui_handler()
        self.tray_icon = None

        # 定时器相关
        self.schedule_timer = QTimer()
        self.schedule_timer.timeout.connect(self.check_scheduled_run)
        self.schedule_timer.start(60000)  # 每分钟检查一次
        self.last_scheduled_run_date = None  # 记录最后一次定时运行的日期

        # 初始化界面
        self.init_ui()
        self.init_components()
        self.init_tray()
        self.setup_logger()

        # 加载配置
        self.load_settings()

        # 输入框已设置左对齐

        # 初始化统计显示
        self.update_data_statistics()
        self.update_usage_display()

        # 使用限制检查已删除



    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("登记设立批量查询 v1.0.0")
        self.setMinimumSize(800, 500)

        # 设置窗口图标
        self.set_window_icon()

        # 应用样式
        self.setStyleSheet(get_complete_stylesheet())

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(12, 12, 12, 12)
        main_layout.setSpacing(12)

        # 创建控制区域
        self.create_control_area(main_layout)

        # 创建日志区域
        self.create_log_area(main_layout)

        # 创建状态栏
        self.create_status_bar()
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 获取程序运行目录
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe
                base_path = sys._MEIPASS
            else:
                # 如果是开发环境
                base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

            # 尝试加载图标文件（优先使用ICO格式）
            icon_paths = [
                os.path.join(base_path, "resources", "icons", "app.ico"),
                os.path.join(base_path, "resources", "icons", "学校查询.ico"),
                os.path.join(base_path, "resources", "icons", "app.png"),
                os.path.join(base_path, "resources", "icons", "学校查询.svg"),
                os.path.join(base_path, "logo", "app.ico"),
                os.path.join(base_path, "logo", "app.png"),
                # 也尝试当前目录
                "resources/icons/app.ico",
                "resources/icons/学校查询.ico",
                "resources/icons/app.png"
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    icon = QIcon(icon_path)
                    if not icon.isNull():
                        self.setWindowIcon(icon)
                        print(f"✅ 成功设置窗口图标: {icon_path}")
                        return

            # 如果没有找到图标文件，使用默认图标
            self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
            print("⚠️ 未找到图标文件，使用默认图标")

        except Exception as e:
            print(f"设置窗口图标失败: {e}")
            # 使用默认图标作为备用
            self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
    

    
    def create_control_area(self, main_layout):
        """创建控制区域"""
        # 查询控制区域
        query_group = QGroupBox("查询控制")
        query_layout = QVBoxLayout(query_group)
        query_layout.setContentsMargins(10, 0, 10, 10)  # 进一步减少上边距，让内容更往上移

        # 查询信息 - 第一行
        info_layout1 = QHBoxLayout()
        region_count = len(get_region_codes())
        range_label = QLabel(f"查询范围：{region_count} 个地区")
        range_label.setStyleSheet("color: #605E5C; font-size: 10pt;")
        info_layout1.addWidget(range_label)
        info_layout1.addStretch()

        # 统计信息 - 分两行显示
        stats_widget = QWidget()
        stats_widget.setStyleSheet("background: transparent; border: none;")  # 移除底纹
        stats_layout = QVBoxLayout(stats_widget)
        stats_layout.setContentsMargins(0, 0, 30, 0)  # 右边距30px，让统计往左移
        stats_layout.setSpacing(12)  # 进一步增大间距

        self.total_records_label = QLabel("总记录数: 0")
        self.total_records_label.setStyleSheet("color: #107C10; font-weight: bold; font-size: 9pt; background: transparent; border: none;")
        stats_layout.addWidget(self.total_records_label)

        self.today_records_label = QLabel("今日新增: 0")
        self.today_records_label.setStyleSheet("color: #FF8C00; font-weight: bold; font-size: 9pt; background: transparent; border: none;")
        stats_layout.addWidget(self.today_records_label)

        self.usage_count_label = QLabel("可用次数: 4/4")
        self.usage_count_label.setStyleSheet("color: #0078D4; font-weight: bold; font-size: 9pt; background: transparent; border: none;")
        stats_layout.addWidget(self.usage_count_label)

        info_layout1.addWidget(stats_widget)

        query_layout.addLayout(info_layout1)

        # 查询信息 - 第二行（减少间距）
        info_layout2 = QHBoxLayout()
        info_layout2.setContentsMargins(0, -8, 0, 0)  # 更大的负上边距，让第二行更靠近第一行
        target_label = QLabel("查询目标：学校和中学")
        target_label.setStyleSheet("color: #605E5C; font-size: 10pt;")
        info_layout2.addWidget(target_label)
        info_layout2.addStretch()

        query_layout.addLayout(info_layout2)

        # 查询按钮 - 简单布局
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(10)  # 增加按钮间距

        # 开始查询按钮
        self.start_btn = QPushButton("开始查询")
        self.start_btn.setFixedSize(80, 35)
        self.start_btn.clicked.connect(self.start_query)
        control_layout.addWidget(self.start_btn)

        # 停止查询按钮
        self.stop_btn = QPushButton("停止查询")
        self.stop_btn.setFixedSize(80, 35)
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_query)
        control_layout.addWidget(self.stop_btn)

        # 添加弹性空间，把按钮推到左边
        control_layout.addStretch()

        query_layout.addLayout(control_layout)

        main_layout.addWidget(query_group)

        # 配置设置区域
        config_group = QGroupBox("配置设置")
        config_layout = QHBoxLayout(config_group)

        # 代理设置
        proxy_layout = QVBoxLayout()

        # 复选框容器，防止拉伸
        proxy_cb_layout = QHBoxLayout()
        self.proxy_enabled_cb = QCheckBox("启用代理池")
        self.proxy_enabled_cb.setStyleSheet("""
            QCheckBox {
                background: transparent;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #999999;
                border-radius: 2px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #0078D4;
                border: 1px solid #0078D4;
                image: none;
            }
            QCheckBox::indicator:hover {
                border: 1px solid #0078D4;
            }
        """)
        proxy_cb_layout.addWidget(self.proxy_enabled_cb)
        proxy_cb_layout.addStretch()  # 添加弹性空间
        proxy_layout.addLayout(proxy_cb_layout)

        # 重写代理配置输入框，强制左对齐
        proxy_form = QHBoxLayout()
        proxy_form.addWidget(QLabel("用户名:"))

        # 用户名输入框
        self.proxy_username_edit = QLineEdit()
        self.proxy_username_edit.setPlaceholderText("快代理用户名")
        self.proxy_username_edit.setAlignment(Qt.AlignLeft)
        proxy_form.addWidget(self.proxy_username_edit)

        proxy_form.addWidget(QLabel("密码:"))

        # 密码输入框
        self.proxy_password_edit = QLineEdit()
        self.proxy_password_edit.setPlaceholderText("快代理密码")
        self.proxy_password_edit.setEchoMode(QLineEdit.Password)
        self.proxy_password_edit.setAlignment(Qt.AlignLeft)
        proxy_form.addWidget(self.proxy_password_edit)

        proxy_layout.addLayout(proxy_form)
        config_layout.addLayout(proxy_layout)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.VLine)
        line.setFrameShadow(QFrame.Sunken)
        config_layout.addWidget(line)

        # 微信推送设置
        wechat_layout = QVBoxLayout()

        # 复选框容器，防止拉伸
        wechat_cb_layout = QHBoxLayout()
        self.wechat_enabled_cb = QCheckBox("启用微信推送")
        self.wechat_enabled_cb.setStyleSheet("""
            QCheckBox {
                background: transparent;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #999999;
                border-radius: 2px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #0078D4;
                border: 1px solid #0078D4;
            }
            QCheckBox::indicator:checked::before {
                content: "✓";
                color: white;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
                line-height: 14px;
            }
            QCheckBox::indicator:hover {
                border: 1px solid #0078D4;
            }
        """)
        wechat_cb_layout.addWidget(self.wechat_enabled_cb)
        wechat_cb_layout.addStretch()  # 添加弹性空间
        wechat_layout.addLayout(wechat_cb_layout)

        wechat_form = QHBoxLayout()
        wechat_form.addWidget(QLabel("Webhook URL:"))

        # Webhook URL输入框
        self.webhook_url_edit = QLineEdit()
        self.webhook_url_edit.setPlaceholderText("企业微信Webhook地址")
        self.webhook_url_edit.setAlignment(Qt.AlignLeft)
        wechat_form.addWidget(self.webhook_url_edit)

        wechat_layout.addLayout(wechat_form)
        config_layout.addLayout(wechat_layout)

        # 定时运行设置
        schedule_layout = QVBoxLayout()

        # 复选框容器，防止拉伸
        schedule_cb_layout = QHBoxLayout()
        self.scheduled_run_enabled_cb = QCheckBox("启用定时运行")
        self.scheduled_run_enabled_cb.setStyleSheet("""
            QCheckBox {
                background: transparent;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #999999;
                border-radius: 2px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #0078D4;
                border: 1px solid #0078D4;
            }
            QCheckBox::indicator:checked::before {
                content: "✓";
                color: white;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
                line-height: 14px;
            }
            QCheckBox::indicator:hover {
                border: 1px solid #0078D4;
            }
        """)
        schedule_cb_layout.addWidget(self.scheduled_run_enabled_cb)
        schedule_cb_layout.addStretch()  # 添加弹性空间
        schedule_layout.addLayout(schedule_cb_layout)

        schedule_form = QHBoxLayout()
        schedule_form.addWidget(QLabel("运行时间:"))

        # 时间设置框
        self.scheduled_run_time_edit = QTimeEdit()
        self.scheduled_run_time_edit.setDisplayFormat("HH:mm")
        self.scheduled_run_time_edit.setTime(QTime(6, 0))  # 默认6:00
        self.scheduled_run_time_edit.setFixedWidth(80)
        schedule_form.addWidget(self.scheduled_run_time_edit)
        schedule_form.addStretch()

        schedule_layout.addLayout(schedule_form)
        config_layout.addLayout(schedule_layout)

        main_layout.addWidget(config_group)
    
    def create_log_area(self, main_layout):
        """创建日志区域"""
        # 日志组
        log_group = QGroupBox("查询日志")
        log_layout = QVBoxLayout(log_group)
        log_layout.setContentsMargins(8, 0, 8, 8)  # 上边距为0
        log_layout.setSpacing(10)  # 增加按钮和日志之间的距离

        # 日志控制按钮
        log_header_layout = QHBoxLayout()
        log_header_layout.addStretch()

        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.setProperty("class", "secondary")
        self.clear_log_btn.setFixedSize(60, 28)
        self.clear_log_btn.clicked.connect(self.clear_log_display)
        log_header_layout.addWidget(self.clear_log_btn)

        self.auto_scroll_btn = QPushButton("自动滚动")
        self.auto_scroll_btn.setProperty("class", "secondary")
        self.auto_scroll_btn.setFixedSize(60, 28)
        self.auto_scroll_btn.setCheckable(True)
        self.auto_scroll_btn.setChecked(True)
        self.auto_scroll_btn.clicked.connect(self.toggle_auto_scroll)
        log_header_layout.addWidget(self.auto_scroll_btn)

        log_layout.addLayout(log_header_layout)

        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Courier New", 9))
        self.log_text.setMinimumHeight(300)

        # 最基础设置
        self.log_text.setAcceptRichText(False)  # 纯文本
        self.log_text.setLineWrapMode(QTextEdit.NoWrap)  # 不自动换行

        log_layout.addWidget(self.log_text)

        main_layout.addWidget(log_group)
    
    def create_status_bar(self):
        """创建状态栏"""
        # 不创建状态栏，去掉底部所有文字
        pass

    def update_usage_status(self):
        """更新使用状态显示 - 已禁用状态栏"""
        pass

    def init_components(self):
        """初始化组件"""
        # 初始化代理管理器（不传入logger，避免线程安全问题）
        proxy_config = self.settings.get_section('proxy')
        self.proxy_manager = create_proxy_manager(proxy_config, None)

        # 强制启用代理
        proxy_config['enabled'] = True
        self.settings.set('proxy.enabled', True)
        self.logger.info("🔧 代理功能已强制启用（程序必需）")

        # 初始化企业微信推送（不传入logger，避免线程安全问题）
        wechat_config = self.settings.get_section('wechat')
        if wechat_config.get('enabled', False):
            self.wechat_notifier = WeChatNotifier(
                wechat_config.get('webhook_url', ''),
                None
            )
        else:
            self.wechat_notifier = None
        
        # 初始化查询引擎 - 不传入logger，避免线程安全问题
        self.query_engine = QueryEngine(self.proxy_manager, None)
    
    def init_tray(self):
        """初始化系统托盘"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return
        
        try:
            self.tray_icon = QSystemTrayIcon(self)
            
            # 设置托盘图标
            if hasattr(self, 'windowIcon') and not self.windowIcon().isNull():
                self.tray_icon.setIcon(self.windowIcon())
            else:
                self.tray_icon.setIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
            
            # 创建托盘菜单
            tray_menu = QMenu()
            
            show_action = tray_menu.addAction("显示主窗口")
            show_action.triggered.connect(self.show_normal_window)
            
            tray_menu.addSeparator()
            
            quit_action = tray_menu.addAction("退出")
            quit_action.triggered.connect(QApplication.quit)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.activated.connect(self.tray_icon_activated)
            
            # 显示托盘图标
            self.tray_icon.show()
            
        except Exception as e:
            self.logger.error(f"初始化系统托盘失败: {e}")
    
    def setup_logger(self):
        """设置日志处理器"""
        self.log_handler.set_text_widget(self.log_text)
        self.logger.set_gui_callback(self.log_handler.append_log)
        
        # 欢迎信息
        self.logger.info("===============应用初始化成功==================")

    def load_settings(self):
        """加载设置"""
        # 使用默认尺寸900x900
        window_width = 900
        window_height = 900

        # 设置窗口尺寸
        self.resize(window_width, window_height)

        # 设置最小尺寸
        self.setMinimumSize(800, 600)
        self.setMaximumSize(1200, 1000)

        # 加载配置到界面 - 代理必须启用
        self.proxy_enabled_cb.setChecked(True)  # 强制启用代理
        self.settings.set('proxy.enabled', True)  # 确保配置文件中也是启用状态
        self.proxy_username_edit.setText(self.settings.get('proxy.api_username', ''))
        self.proxy_password_edit.setText(self.settings.get('proxy.api_password', ''))

        self.wechat_enabled_cb.setChecked(self.settings.get('wechat.enabled', False))
        self.webhook_url_edit.setText(self.settings.get('wechat.webhook_url', ''))

        # 加载定时运行设置
        self.scheduled_run_enabled_cb.setChecked(self.settings.get('app.scheduled_run_enabled', False))
        time_str = self.settings.get('app.scheduled_run_time', '06:00')
        time_obj = QTime.fromString(time_str, 'HH:mm')
        if time_obj.isValid():
            self.scheduled_run_time_edit.setTime(time_obj)
        else:
            self.scheduled_run_time_edit.setTime(QTime(6, 0))

        # 连接信号
        self.proxy_enabled_cb.toggled.connect(self.on_proxy_toggled)
        self.proxy_username_edit.textChanged.connect(self.on_config_changed)
        self.proxy_password_edit.textChanged.connect(self.on_config_changed)
        self.wechat_enabled_cb.toggled.connect(self.on_config_changed)
        self.webhook_url_edit.textChanged.connect(self.on_config_changed)
        self.scheduled_run_enabled_cb.toggled.connect(self.on_config_changed)
        self.scheduled_run_time_edit.timeChanged.connect(self.on_config_changed)

        # 更新状态显示
        self.update_status_display()

    # 使用限制检查功能已删除

    def update_status_display(self):
        """更新状态显示"""
        # 更新复选框状态（如果需要）
        pass

    # 使用次数显示功能已删除

    def update_data_statistics(self):
        """更新数据统计"""
        try:
            data = self.data_manager.load_data()

            # 统计包含"学校"或"中学"的记录数
            school_records = 0
            today_school_records = 0

            from datetime import date
            today_str = date.today().isoformat()

            for record in data:
                unit_name = record.get('单位名称', '')
                # 检查单位名称是否包含"学校"或"中学"
                if '学校' in unit_name or '中学' in unit_name:
                    school_records += 1
                    # 如果是今日记录，也计入今日统计
                    if record.get('通知时间', '').startswith(today_str):
                        today_school_records += 1

            # 更新显示
            self.total_records_label.setText(f"总记录数: {school_records}")

            # 如果正在查询，显示本次新增数量；否则显示今日总数
            if hasattr(self, 'query_thread') and self.query_thread and self.query_thread.isRunning():
                # 查询中，显示本次新增的学校记录
                new_school_total = school_records - self.query_start_total
                new_school_today = today_school_records - self.query_start_today
                self.today_records_label.setText(f"今日新增: {new_school_today}")
            else:
                # 非查询状态，显示今日学校记录总数
                self.today_records_label.setText(f"今日新增: {today_school_records}")

        except Exception as e:
            self.logger.error(f"更新数据统计失败: {e}")

    def update_usage_display(self):
        """更新可用次数显示"""
        try:
            remaining = self.usage_limiter.get_remaining_uses()
            max_uses = self.usage_limiter.max_daily_uses
            self.usage_count_label.setText(f"可用次数: {remaining}/{max_uses}")

            # 根据剩余次数设置颜色
            if remaining == 0:
                self.usage_count_label.setStyleSheet("color: #D83B01; font-weight: bold; font-size: 9pt; background: transparent; border: none;")  # 红色
            elif remaining == 1:
                self.usage_count_label.setStyleSheet("color: #FF8C00; font-weight: bold; font-size: 9pt; background: transparent; border: none;")  # 橙色
            else:
                self.usage_count_label.setStyleSheet("color: #0078D4; font-weight: bold; font-size: 9pt; background: transparent; border: none;")  # 蓝色
        except Exception as e:
            self.usage_count_label.setText("可用次数: 未知")
            self.usage_count_label.setStyleSheet("color: #605E5C; font-weight: bold; font-size: 9pt; background: transparent; border: none;")

    def on_proxy_toggled(self, checked):
        """代理勾选状态改变处理"""
        if not checked:
            # 如果用户尝试取消勾选，显示警告并重新勾选
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                "代理设置",
                "程序必须启用代理才能使用!"
            )
            # 重新勾选
            self.proxy_enabled_cb.setChecked(True)
            return

        # 如果是勾选状态，正常处理
        self.on_config_changed()

    def on_config_changed(self):
        """配置更改处理"""
        # 保存配置到设置 - 代理始终启用
        self.settings.set('proxy.enabled', True)  # 强制保持启用状态
        self.settings.set('proxy.api_username', self.proxy_username_edit.text())
        self.settings.set('proxy.api_password', self.proxy_password_edit.text())
        self.settings.set('wechat.enabled', self.wechat_enabled_cb.isChecked())
        self.settings.set('wechat.webhook_url', self.webhook_url_edit.text())

        # 保存定时运行设置
        self.settings.set('app.scheduled_run_enabled', self.scheduled_run_enabled_cb.isChecked())
        self.settings.set('app.scheduled_run_time', self.scheduled_run_time_edit.time().toString('HH:mm'))

        # 保存到文件
        self.settings.save_config()

        # 重新初始化组件
        self.init_components()

    def validate_proxy_config(self):
        """验证代理配置"""
        try:
            from PyQt5.QtWidgets import QMessageBox

            # 检查代理是否启用
            proxy_enabled = self.settings.get('proxy.enabled', False)

            if not proxy_enabled:
                QMessageBox.warning(
                    self,
                    "代理配置错误",
                    "程序必须启用代理才能使用!\n\n请在配置区域勾选'启用代理'并填写快代理API信息。"
                )
                return False

            # 检查快代理API配置
            username = self.settings.get('proxy.api_username', '').strip()
            password = self.settings.get('proxy.api_password', '').strip()

            if not username or not password:
                QMessageBox.warning(
                    self,
                    "代理配置错误",
                    "程序必须配置快代理API才能使用!\n\n请填写快代理的用户名和密码。\n\n如果您还没有快代理账号，请访问:\nhttps://www.kuaidaili.com/"
                )
                return False

            # 检查代理管理器是否有可用代理
            if not self.proxy_manager:
                self.logger.error("代理管理器未初始化")
                QMessageBox.warning(
                    self,
                    "代理配置错误",
                    "代理管理器未初始化，请检查配置后重试。"
                )
                return False

            # 尝试获取代理 - 使用更短的超时时间
            try:
                # 在Windows上使用线程超时而不是signal
                import threading
                import time

                proxy_result = [None]
                proxy_error = [None]

                def get_proxy_thread():
                    try:
                        proxy_result[0] = self.proxy_manager.get_proxy()
                    except Exception as e:
                        proxy_error[0] = e

                proxy_thread = threading.Thread(target=get_proxy_thread)
                proxy_thread.daemon = True
                proxy_thread.start()
                proxy_thread.join(timeout=10)  # 10秒超时

                if proxy_thread.is_alive():
                    QMessageBox.warning(
                        self,
                        "代理获取超时",
                        "代理获取操作超时!\n\n可能的原因:\n1. 网络连接缓慢\n2. 快代理服务器响应慢\n\n请检查网络连接后重试。"
                    )
                    return False

                if proxy_error[0]:
                    raise proxy_error[0]

                proxy = proxy_result[0]
                if not proxy:
                    QMessageBox.warning(
                        self,
                        "代理获取失败",
                        "无法获取有效代理!\n\n可能的原因:\n1. 快代理API配置错误\n2. 快代理账户余额不足\n3. 网络连接问题\n\n请检查配置后重试。"
                    )
                    return False

            except Exception as e:
                self.logger.error(f"代理验证失败: {e}")

                QMessageBox.warning(
                    self,
                    "代理验证失败",
                    f"代理验证过程中出现错误:\n{str(e)}\n\n请检查快代理配置后重试。"
                )
                return False

            return True

        except Exception as e:
            self.logger.error(f"代理配置验证过程异常: {e}")
            import traceback
            self.logger.error(f"验证过程异常详情: {traceback.format_exc()}")

            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(
                    self,
                    "系统错误",
                    f"代理配置验证过程中发生系统错误:\n{str(e)}\n\n请重启程序后重试。"
                )
            except:
                print(f"严重错误 - 代理配置验证失败且无法显示错误对话框: {e}")

            return False

    def start_query(self):
        """开始查询"""
        try:
            # 检查查询线程状态
            if self.query_thread and self.query_thread.isRunning():
                self.logger.warning("查询已在进行中，请稍候...")
                return

            # 检查代理配置
            if not self.validate_proxy_config():
                return

            # 检查使用次数限制
            if not check_usage_limit(self.usage_limiter):
                return  # 超出限制，不执行查询

            # 记录本次查询使用
            record_query_usage(self.usage_limiter)

            # 更新可用次数显示
            self.update_usage_display()

            # 记录查询开始时的统计数据
            self.record_query_start_stats()

            # 检查查询引擎
            if not self.query_engine:
                self.logger.error("查询引擎未初始化，请重启程序")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(self, "系统错误", "查询引擎未初始化，请重启程序")
                return

            # 更新查询引擎配置
            self.query_engine.proxy_manager = self.proxy_manager

            # 创建查询线程
            self.query_thread = QueryThread(self.query_engine)

            # 连接信号
            self.query_thread.progress_updated.connect(self.on_query_progress)
            self.query_thread.query_finished.connect(self.on_query_finished)
            self.query_thread.new_data_found.connect(self.on_new_data_found)
            self.query_thread.log_message.connect(self.on_thread_log)  # 连接线程安全日志

            # 更新界面状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)

            # 开始查询
            self.logger.info("开始查询...")
            self.query_thread.start()

        except Exception as e:
            # 捕获所有异常，防止程序闪退
            error_msg = f"启动查询时发生异常: {e}"
            self.logger.error(error_msg)

            import traceback
            traceback_str = traceback.format_exc()
            self.logger.error(f"异常详情: {traceback_str}")

            # 恢复界面状态
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)

            # 显示错误对话框
            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(
                    self,
                    "查询启动失败",
                    f"启动查询时发生错误:\n{str(e)}\n\n请检查日志文件获取详细信息。"
                )
            except Exception as dialog_e:
                self.logger.error(f"显示错误对话框失败: {dialog_e}")
                print(f"严重错误 - 无法显示错误对话框: {dialog_e}")
                print(f"原始错误: {e}")

    def record_query_start_stats(self):
        """记录查询开始时的统计数据"""
        try:
            data = self.data_manager.load_data()

            # 统计查询开始时包含"学校"或"中学"的记录数
            school_records = 0
            today_school_records = 0

            from datetime import date
            today_str = date.today().isoformat()

            for record in data:
                unit_name = record.get('单位名称', '')
                # 检查单位名称是否包含"学校"或"中学"
                if '学校' in unit_name or '中学' in unit_name:
                    school_records += 1
                    # 如果是今日记录，也计入今日统计
                    if record.get('通知时间', '').startswith(today_str):
                        today_school_records += 1

            self.query_start_total = school_records
            self.query_start_today = today_school_records

        except Exception as e:
            self.logger.error(f"记录查询开始统计失败: {e}")
            self.query_start_total = 0
            self.query_start_today = 0

    def stop_query(self):
        """停止查询"""
        if self.query_thread and self.query_thread.isRunning():
            self.query_thread.stop()
            self.query_thread.wait(5000)  # 等待5秒

            if self.query_thread.isRunning():
                self.query_thread.terminate()
                self.query_thread.wait()

        self.on_query_finished({"success": False, "message": "用户停止查询"})

    def on_thread_log(self, message: str, level: str):
        """处理来自线程的日志消息 - 线程安全"""
        try:
            if level == "SUCCESS":
                self.logger.success(message)
            elif level == "WARNING":
                self.logger.warning(message)
            elif level == "ERROR":
                self.logger.error(message)
            elif level == "DEBUG":
                self.logger.debug(message)
            else:
                self.logger.info(message)
        except Exception as e:
            print(f"线程日志处理失败: {e}")

    def on_query_progress(self, current, total, region_name):
        """查询进度更新"""
        # 状态栏已禁用，不显示进度
        pass

    def on_query_finished(self, result):
        """查询完成"""
        try:
            # 更新界面状态
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)

            if result.get('success', False):
                # self.logger.success("批量查询完成！")

                # 汇总通知已禁用
                # if self.wechat_notifier and self.settings.get('wechat.send_summary', True):
                #     self.wechat_notifier.send_summary_notification(result)
                pass
            else:
                error_msg = result.get('message', '未知错误')
                self.logger.error(f"查询失败: {error_msg}")
        except Exception as e:
            self.logger.error(f"处理查询结果时出错: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            error_msg = result.get('error', result.get('message', '未知错误'))
            self.logger.error(f"查询失败: {error_msg}")

        # 更新统计信息
        self.update_data_statistics()

        # 清理线程
        if self.query_thread:
            self.query_thread.deleteLater()
            self.query_thread = None

    def on_new_data_found(self, data, region_name):
        """发现新数据"""
        try:
            self.logger.success(f"{region_name}: 发现 {len(data)} 条新记录")

            # 立即推送微信通知 - 添加异常处理，避免微信通知失败导致程序崩溃
            if self.wechat_notifier:
                try:
                    self.wechat_notifier.send_notification(data, region_name)
                except Exception as wechat_e:
                    self.logger.error(f"微信通知发送失败: {wechat_e}")
                    # 微信通知失败不应该影响程序继续运行

            # 更新统计信息
            self.update_data_statistics()

        except Exception as e:
            self.logger.error(f"处理新数据时发生异常: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")

    def clear_log_display(self):
        """清空日志显示"""
        self.log_handler.clear_logs()

    def toggle_auto_scroll(self):
        """切换自动滚动"""
        if self.auto_scroll_btn.isChecked():
            self.auto_scroll_btn.setText("自动滚动")
        else:
            self.auto_scroll_btn.setText("手动滚动")

    def tray_icon_activated(self, reason):
        """托盘图标激活处理"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show_normal_window()

    def show_normal_window(self):
        """显示正常窗口"""
        self.show()
        self.raise_()
        self.activateWindow()

    def closeEvent(self, event):
        """关闭事件处理"""
        if self.settings.get('app.minimize_to_tray', True) and self.tray_icon and self.tray_icon.isVisible():
            # 最小化到托盘
            self.hide()
            if hasattr(self, '_first_minimize'):
                self.tray_icon.showMessage(
                    "登记设立批量查询",
                    "程序已最小化到系统托盘",
                    QSystemTrayIcon.Information,
                    2000
                )
                self._first_minimize = False
            event.ignore()
        else:
            # 真正退出
            self.save_window_state()

            # 停止查询
            if self.query_thread and self.query_thread.isRunning():
                self.stop_query()

            event.accept()

    def save_window_state(self):
        """保存窗口状态"""
        try:
            self.settings.set('ui.window_width', self.width())
            self.settings.set('ui.window_height', self.height())
            self.settings.save_config()
        except Exception as e:
            self.logger.error(f"保存窗口状态失败: {e}")

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)

        # 延迟保存窗口状态
        if hasattr(self, '_resize_timer'):
            self._resize_timer.stop()
        else:
            self._resize_timer = QTimer()
            self._resize_timer.setSingleShot(True)
            self._resize_timer.timeout.connect(self.save_window_state)

        self._resize_timer.start(1000)  # 1秒后保存

    def check_scheduled_run(self):
        """检查是否需要定时运行"""
        try:
            # 检查定时运行是否启用
            if not self.settings.get('app.scheduled_run_enabled', False):
                return

            # 获取当前时间和设置的运行时间
            current_time = QTime.currentTime()
            scheduled_time_str = self.settings.get('app.scheduled_run_time', '06:00')
            scheduled_time = QTime.fromString(scheduled_time_str, 'HH:mm')

            # 检查时间是否匹配（精确到分钟）
            if (current_time.hour() == scheduled_time.hour() and
                current_time.minute() == scheduled_time.minute()):

                # 检查今天是否已经定时运行过
                from datetime import datetime
                today = datetime.now().date()

                if self.last_scheduled_run_date == today:
                    return  # 今天已经运行过了

                # 检查使用次数限制
                if not check_usage_limit(self.usage_limiter):
                    self.log_message("⏰ 定时运行跳过，今日已达运行次数限制", "WARNING")
                    return

                # 检查是否正在查询
                if self.query_thread and self.query_thread.isRunning():
                    self.log_message("⏰ 定时运行跳过，查询正在进行中", "INFO")
                    return

                # 开始定时运行
                self.log_message(f"⏰ 定时运行开始（{scheduled_time_str}）", "INFO")
                self.last_scheduled_run_date = today
                self.start_query()

        except Exception as e:
            self.logger.error(f"定时运行检查失败: {e}")
