"""
数据管理模块
负责数据的读取、保存、统计等功能
"""
import json
import os
import shutil
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import csv


class DataManager:
    """数据管理器"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self.data_file = "extracted_data.json"
        self.backup_dir = "data_backup"
        
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        if self.logger:
            self.logger.log(message, level)
        else:
            print(f"[{level}] {message}")
    
    def ensure_backup_dir(self):
        """确保备份目录存在"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def load_data(self, file_path: str = None) -> List[Dict]:
        """加载数据"""
        if file_path is None:
            file_path = self.data_file
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return []
                return json.loads(content)
        except FileNotFoundError:
            self.log(f"数据文件 {file_path} 不存在，返回空列表")
            return []
        except json.JSONDecodeError as e:
            self.log(f"数据文件 {file_path} 格式错误: {e}", "ERROR")
            return []
        except Exception as e:
            self.log(f"加载数据文件 {file_path} 时出错: {e}", "ERROR")
            return []
    
    def save_data(self, data: List[Dict], file_path: str = None, backup: bool = True) -> bool:
        """保存数据"""
        if file_path is None:
            file_path = self.data_file
            
        try:
            # 创建备份
            if backup and os.path.exists(file_path):
                self.create_backup(file_path)
            
            # 保存数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.log(f"数据已保存到 {file_path}，共 {len(data)} 条记录")
            return True
            
        except Exception as e:
            self.log(f"保存数据到 {file_path} 时出错: {e}", "ERROR")
            return False
    
    def append_data(self, new_data: List[Dict], file_path: str = None) -> List[Dict]:
        """追加数据"""
        if file_path is None:
            file_path = self.data_file
            
        # 加载现有数据
        existing_data = self.load_data(file_path)
        
        # 合并数据
        all_data = existing_data + new_data
        
        # 保存合并后的数据
        if self.save_data(all_data, file_path):
            return all_data
        else:
            return existing_data
    
    def create_backup(self, file_path: str = None) -> str:
        """创建数据备份"""
        if file_path is None:
            file_path = self.data_file
            
        if not os.path.exists(file_path):
            return ""
            
        try:
            self.ensure_backup_dir()
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_{timestamp}_{os.path.basename(file_path)}"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # 复制文件
            shutil.copy2(file_path, backup_path)
            
            self.log(f"数据备份已创建: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.log(f"创建备份时出错: {e}", "ERROR")
            return ""
    
    def get_data_statistics(self, data: List[Dict] = None) -> Dict:
        """获取数据统计信息"""
        if data is None:
            data = self.load_data()
        
        if not data:
            return {
                "total_records": 0,
                "regions": {},
                "application_types": {},
                "latest_date": None,
                "earliest_date": None
            }
        
        # 统计各地区记录数
        regions = {}
        application_types = {}
        dates = []
        
        for record in data:
            # 地区统计
            region = record.get('地区名称', '未知地区')
            regions[region] = regions.get(region, 0) + 1
            
            # 申办事项统计
            app_type = record.get('申办事项', '未知类型')
            application_types[app_type] = application_types.get(app_type, 0) + 1
            
            # 日期统计
            notify_date = record.get('通知时间')
            if notify_date:
                dates.append(notify_date)
        
        # 排序日期
        dates.sort()
        
        return {
            "total_records": len(data),
            "regions": dict(sorted(regions.items(), key=lambda x: x[1], reverse=True)),
            "application_types": dict(sorted(application_types.items(), key=lambda x: x[1], reverse=True)),
            "latest_date": dates[-1] if dates else None,
            "earliest_date": dates[0] if dates else None
        }
    
    def export_to_csv(self, data: List[Dict] = None, file_path: str = None) -> bool:
        """导出数据到CSV文件"""
        if data is None:
            data = self.load_data()
        
        if not data:
            self.log("没有数据可导出", "WARNING")
            return False
        
        if file_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"exported_data_{timestamp}.csv"
        
        try:
            # 获取所有字段名
            fieldnames = set()
            for record in data:
                fieldnames.update(record.keys())
            fieldnames = sorted(list(fieldnames))
            
            # 写入CSV文件
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            self.log(f"数据已导出到 {file_path}，共 {len(data)} 条记录")
            return True
            
        except Exception as e:
            self.log(f"导出CSV文件时出错: {e}", "ERROR")
            return False
    
    def filter_data(self, data: List[Dict] = None, filters: Dict = None) -> List[Dict]:
        """过滤数据"""
        if data is None:
            data = self.load_data()
        
        if not filters:
            return data
        
        filtered_data = []
        
        for record in data:
            match = True
            
            # 地区过滤
            if 'region' in filters and filters['region']:
                if record.get('地区名称', '') != filters['region']:
                    match = False
            
            # 申办事项过滤
            if 'application_type' in filters and filters['application_type']:
                if record.get('申办事项', '') != filters['application_type']:
                    match = False
            
            # 日期范围过滤
            if 'date_from' in filters and filters['date_from']:
                if record.get('通知时间', '') < filters['date_from']:
                    match = False
            
            if 'date_to' in filters and filters['date_to']:
                if record.get('通知时间', '') > filters['date_to']:
                    match = False
            
            # 关键词过滤
            if 'keyword' in filters and filters['keyword']:
                keyword = filters['keyword'].lower()
                unit_name = record.get('单位名称', '').lower()
                if keyword not in unit_name:
                    match = False
            
            if match:
                filtered_data.append(record)
        
        return filtered_data
    
    def remove_duplicates(self, data: List[Dict] = None, key_field: str = '统一社会信用代码') -> List[Dict]:
        """去除重复数据"""
        if data is None:
            data = self.load_data()

        seen_codes = set()  # 有统一社会信用代码的记录
        seen_records_without_code = set()  # 无统一社会信用代码的记录
        unique_data = []

        for record in data:
            key_value = record.get(key_field)

            if key_value:
                # 有统一社会信用代码，按代码去重
                if key_value not in seen_codes:
                    seen_codes.add(key_value)
                    unique_data.append(record)
            else:
                # 无统一社会信用代码，使用组合键去重
                unit_name = record.get('单位名称', '')
                region_name = record.get('地区名称', '')
                notify_time = record.get('通知时间', '')

                if unit_name:  # 至少要有单位名称
                    record_key = f"{unit_name}|{region_name}|{notify_time}"
                    if record_key not in seen_records_without_code:
                        seen_records_without_code.add(record_key)
                        unique_data.append(record)
                        self.log(f"保留无代码记录: {unit_name}", "INFO")
                else:
                    # 单位名称也没有，直接保留（可能是数据错误）
                    unique_data.append(record)
                    self.log(f"保留异常记录: 单位名称和统一社会信用代码均缺失", "WARNING")

        removed_count = len(data) - len(unique_data)
        if removed_count > 0:
            self.log(f"已去除 {removed_count} 条重复记录")

        return unique_data
    
    def cleanup_old_backups(self, keep_days: int = 30):
        """清理旧备份文件"""
        if not os.path.exists(self.backup_dir):
            return
        
        try:
            current_time = datetime.now()
            removed_count = 0
            
            for filename in os.listdir(self.backup_dir):
                file_path = os.path.join(self.backup_dir, filename)
                if os.path.isfile(file_path):
                    # 获取文件修改时间
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    days_old = (current_time - file_time).days
                    
                    if days_old > keep_days:
                        os.remove(file_path)
                        removed_count += 1
            
            if removed_count > 0:
                self.log(f"已清理 {removed_count} 个旧备份文件")
                
        except Exception as e:
            self.log(f"清理备份文件时出错: {e}", "ERROR")
