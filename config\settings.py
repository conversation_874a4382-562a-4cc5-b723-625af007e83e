"""
配置管理模块
管理应用程序的各种配置项
"""
import os
import json
from typing import Dict, Any, Optional
from datetime import datetime


class Settings:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config/config.json"):
        self.config_file = config_file
        self.config_data = {}
        self.default_config = self._get_default_config()
        
        # 确保配置目录存在
        config_dir = os.path.dirname(config_file)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir)
        
        # 加载配置
        self.load_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 应用程序设置
            "app": {
                "name": "登记设立批量查询",
                "version": "1.0.0",
                "max_daily_runs": 4,
                "auto_start_query": False,
                "minimize_to_tray": True,
                "auto_save_interval": 300,  # 自动保存间隔（秒）
                "log_level": "INFO",
                "scheduled_run_enabled": False,  # 定时运行开关
                "scheduled_run_time": "06:00"    # 定时运行时间
            },
            
            # 查询设置
            "query": {
                "max_retries": 3,
                "retry_delay_min": 3,
                "retry_delay_max": 6,
                "query_delay_min": 1,
                "query_delay_max": 3,
                "proxy_switch_interval_min": 8,
                "proxy_switch_interval_max": 12,
                "timeout": 10,
                "region_file": "region_code.json",
                "output_file": "extracted_data.json"
            },
            
            # 代理设置
            "proxy": {
                "enabled": True,
                "api_username": "",  # 请替换为真实的快代理用户名
                "api_password": "",  # 请替换为真实的快代理密码
                "static_proxies": [],
                "test_url": "https://www.baidu.com",
                "test_timeout": 10
            },
            
            # 企业微信设置
            "wechat": {
                "enabled": False,
                "webhook_url": "",
                "send_summary": False,
                "send_errors": True,
                "rate_limit_delay": 1
            },
            
            # 界面设置
            "ui": {
                "window_width": 900,
                "window_height": 800,
                "window_x": -1,  # -1表示居中
                "window_y": -1,  # -1表示居中
                "font_family": "Segoe UI Variable",
                "font_size": 9,
                "theme": "light",
                "log_max_lines": 1000,
                "auto_scroll_log": True
            },
            
            # 数据设置
            "data": {
                "auto_backup": True,
                "backup_interval_days": 7,
                "keep_backup_days": 30,
                "export_format": "json",
                "auto_cleanup": True
            },
            
            # 日志设置
            "logging": {
                "log_to_file": True,
                "log_directory": "logs",
                "max_file_size": 10485760,  # 10MB
                "backup_count": 5,
                "keep_log_days": 30
            }
        }
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # 合并默认配置和加载的配置
                self.config_data = self._merge_config(self.default_config, loaded_config)
            else:
                # 使用默认配置
                self.config_data = self.default_config.copy()
                # 保存默认配置到文件
                self.save_config()
                
        except (json.JSONDecodeError, FileNotFoundError, PermissionError) as e:
            print(f"加载配置文件失败: {e}")
            self.config_data = self.default_config.copy()
    
    def _merge_config(self, default: Dict, loaded: Dict) -> Dict:
        """合并配置，确保所有默认键都存在"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result:
                if isinstance(value, dict) and isinstance(result[key], dict):
                    result[key] = self._merge_config(result[key], value)
                else:
                    result[key] = value
            else:
                result[key] = value
        
        return result
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            # 添加保存时间戳
            self.config_data['_metadata'] = {
                'last_saved': datetime.now().isoformat(),
                'version': self.config_data['app']['version']
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except (PermissionError, OSError) as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值，支持点分隔的路径"""
        try:
            keys = key_path.split('.')
            value = self.config_data
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """设置配置值，支持点分隔的路径"""
        try:
            keys = key_path.split('.')
            config = self.config_data
            
            # 导航到最后一级的父级
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = value
            return True
            
        except (KeyError, TypeError) as e:
            print(f"设置配置值失败: {e}")
            return False
    
    def get_section(self, section: str) -> Dict:
        """获取配置段"""
        return self.config_data.get(section, {})
    
    def set_section(self, section: str, data: Dict) -> bool:
        """设置配置段"""
        try:
            self.config_data[section] = data
            return True
        except Exception as e:
            print(f"设置配置段失败: {e}")
            return False
    
    def reset_to_default(self, section: Optional[str] = None) -> bool:
        """重置配置到默认值"""
        try:
            if section:
                if section in self.default_config:
                    self.config_data[section] = self.default_config[section].copy()
            else:
                self.config_data = self.default_config.copy()
            
            return True
            
        except Exception as e:
            print(f"重置配置失败: {e}")
            return False
    
    def validate_config(self) -> Dict[str, list]:
        """验证配置有效性"""
        errors = {}
        
        # 验证企业微信配置
        if self.get('wechat.enabled'):
            webhook_url = self.get('wechat.webhook_url', '')
            if not webhook_url or not webhook_url.startswith('http'):
                errors.setdefault('wechat', []).append('webhook_url无效')
        
        # 验证代理配置
        if self.get('proxy.enabled'):
            username = self.get('proxy.api_username', '')
            password = self.get('proxy.api_password', '')
            if not username or not password:
                errors.setdefault('proxy', []).append('API用户名或密码为空')
        
        # 验证文件路径
        region_file = self.get('query.region_file', '')
        if region_file and not os.path.exists(region_file):
            errors.setdefault('query', []).append(f'地区文件不存在: {region_file}')
        
        # 验证数值范围
        max_retries = self.get('query.max_retries', 3)
        if not isinstance(max_retries, int) or max_retries < 1:
            errors.setdefault('query', []).append('最大重试次数必须是正整数')
        
        return errors
    
    def export_config(self, file_path: str) -> bool:
        """导出配置到指定文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """从指定文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并配置
            self.config_data = self._merge_config(self.default_config, imported_config)
            return True
            
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def get_config_info(self) -> Dict:
        """获取配置信息"""
        return {
            'config_file': self.config_file,
            'file_exists': os.path.exists(self.config_file),
            'file_size': os.path.getsize(self.config_file) if os.path.exists(self.config_file) else 0,
            'last_modified': datetime.fromtimestamp(os.path.getmtime(self.config_file)).isoformat() 
                           if os.path.exists(self.config_file) else None,
            'sections': list(self.config_data.keys()),
            'validation_errors': self.validate_config()
        }


# 全局配置实例
_global_settings = None

def get_settings() -> Settings:
    """获取全局配置实例"""
    global _global_settings
    if _global_settings is None:
        _global_settings = Settings()
    return _global_settings

def setup_settings(config_file: str = "config/config.json") -> Settings:
    """设置全局配置实例"""
    global _global_settings
    _global_settings = Settings(config_file)
    return _global_settings
