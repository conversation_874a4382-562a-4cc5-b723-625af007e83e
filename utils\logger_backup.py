"""
日志管理模块
提供GUI日志显示和文件日志记录功能
"""
import os
import logging
from datetime import datetime
from typing import Optional, Callable
from logging.handlers import RotatingFileHandler


class Logger:
    """日志管理器"""
    
    def __init__(self, log_dir: str = "logs", max_file_size: int = 10*1024*1024, 
                 backup_count: int = 5):
        self.log_dir = log_dir
        self.gui_callback = None
        self.log_file = None
        self.file_logger = None
        
        # 确保日志目录存在
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置日志文件
        self.setup_file_logger(max_file_size, backup_count)
    
    def setup_file_logger(self, max_file_size: int, backup_count: int):
        """设置文件日志记录器"""
        try:
            # 创建日志文件名
            timestamp = datetime.now().strftime("%Y%m%d")
            self.log_file = os.path.join(self.log_dir, f"app_{timestamp}.log")
            
            # 创建文件日志记录器
            self.file_logger = logging.getLogger('AppLogger')
            self.file_logger.setLevel(logging.DEBUG)
            
            # 清除现有处理器
            for handler in self.file_logger.handlers[:]:
                self.file_logger.removeHandler(handler)
            
            # 创建旋转文件处理器
            file_handler = RotatingFileHandler(
                self.log_file, 
                maxBytes=max_file_size, 
                backupCount=backup_count,
                encoding='utf-8'
            )
            
            # 设置日志格式
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            # 添加处理器
            self.file_logger.addHandler(file_handler)
            
        except Exception as e:
            print(f"设置文件日志记录器失败: {e}")
    
    def set_gui_callback(self, callback: Callable[[str, str], None]):
        """设置GUI回调函数"""
        self.gui_callback = callback
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        # 输出到GUI
        if self.gui_callback:
            try:
                self.gui_callback(formatted_message, level)
            except Exception as e:
                print(f"GUI日志回调失败: {e}")
        
        # 输出到文件
        if self.file_logger:
            try:
                log_level = getattr(logging, level.upper(), logging.INFO)
                self.file_logger.log(log_level, message)
            except Exception as e:
                print(f"文件日志记录失败: {e}")
        
        # 输出到控制台（调试用）
        print(formatted_message)
    
    def info(self, message: str):
        """记录信息日志"""
        self.log(message, "INFO")
    
    def warning(self, message: str):
        """记录警告日志"""
        self.log(message, "WARNING")
    
    def error(self, message: str):
        """记录错误日志"""
        self.log(message, "ERROR")
    
    def debug(self, message: str):
        """记录调试日志"""
        self.log(message, "DEBUG")
    
    def success(self, message: str):
        """记录成功日志"""
        self.log(message, "SUCCESS")
    
    def get_log_files(self) -> list:
        """获取所有日志文件列表"""
        try:
            log_files = []
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.log'):
                    file_path = os.path.join(self.log_dir, filename)
                    file_size = os.path.getsize(file_path)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    log_files.append({
                        'name': filename,
                        'path': file_path,
                        'size': file_size,
                        'modified': file_time
                    })
            
            # 按修改时间排序
            log_files.sort(key=lambda x: x['modified'], reverse=True)
            return log_files
            
        except Exception as e:
            self.error(f"获取日志文件列表失败: {e}")
            return []
    
    def read_log_file(self, file_path: str, max_lines: int = 1000) -> str:
        """读取日志文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 限制行数
            if len(lines) > max_lines:
                lines = lines[-max_lines:]
                content = f"... (显示最后 {max_lines} 行)\n" + "".join(lines)
            else:
                content = "".join(lines)
                
            return content
            
        except Exception as e:
            return f"读取日志文件失败: {e}"
    
    def clear_old_logs(self, keep_days: int = 30):
        """清理旧日志文件"""
        try:
            current_time = datetime.now()
            removed_count = 0
            
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.log'):
                    file_path = os.path.join(self.log_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    days_old = (current_time - file_time).days
                    
                    if days_old > keep_days:
                        os.remove(file_path)
                        removed_count += 1
            
            if removed_count > 0:
                self.info(f"已清理 {removed_count} 个旧日志文件")
                
        except Exception as e:
            self.error(f"清理日志文件失败: {e}")
    
    def get_log_stats(self) -> dict:
        """获取日志统计信息"""
        try:
            log_files = self.get_log_files()
            total_size = sum(f['size'] for f in log_files)
            
            return {
                'file_count': len(log_files),
                'total_size': total_size,
                'current_log_file': self.log_file,
                'log_directory': self.log_dir
            }
            
        except Exception as e:
            self.error(f"获取日志统计失败: {e}")
            return {}


class GUILogHandler:
    """GUI日志处理器"""
    
    def __init__(self, text_widget=None, max_lines: int = 1000):
        self.text_widget = text_widget
        self.max_lines = max_lines
        self.line_count = 0
        
        # 日志级别颜色映射
        self.level_colors = {
            'INFO': '#000000',      # 黑色
            'SUCCESS': '#008000',   # 绿色
            'WARNING': '#FF8C00',   # 橙色
            'ERROR': '#FF0000',     # 红色
            'DEBUG': '#808080'      # 灰色
        }
    
    def set_text_widget(self, text_widget):
        """设置文本控件"""
        self.text_widget = text_widget
    
    def append_log(self, message: str, level: str = "INFO"):
        """追加日志到GUI"""
        if not self.text_widget:
            return

        try:
            # 检查行数限制
            if self.line_count >= self.max_lines:
                # PyQt5方式：清空文本重新开始
                self.text_widget.clear()
                self.line_count = 0

            # 获取颜色
            color = self.level_colors.get(level, '#000000')

            # 确保消息是完整的字符串，处理编码问题
            if isinstance(message, bytes):
                message = message.decode('utf-8', errors='replace')

            # 确保消息不会太长，避免显示问题
            if len(message) > 1000:
                message = message[:997] + "..."

            # 使用简单的append方法
            self.text_widget.append(message)

            # 自动滚动到底部 - 使用最简单的方法
            try:
                scrollbar = self.text_widget.verticalScrollBar()
                if scrollbar:
                    scrollbar.setValue(scrollbar.maximum())
            except Exception:
                pass  # 忽略滚动错误

            self.line_count += 1

        except Exception as e:
            print(f"GUI日志显示失败: {e}")
    
    def clear_logs(self):
        """清空日志显示"""
        if self.text_widget:
            try:
                self.text_widget.clear()
                self.line_count = 0
            except Exception as e:
                print(f"清空GUI日志失败: {e}")


# 全局日志实例
_global_logger = None

def get_logger() -> Logger:
    """获取全局日志实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = Logger()
    return _global_logger

def setup_logger(log_dir: str = "logs", gui_callback: Optional[Callable] = None) -> Logger:
    """设置全局日志实例"""
    global _global_logger
    _global_logger = Logger(log_dir)
    if gui_callback:
        _global_logger.set_gui_callback(gui_callback)
    return _global_logger
