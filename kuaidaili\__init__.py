"""
快代理SDK - 轻量级快代理API客户端
====================================

这是一个从中国社会组织监控工具项目中提取的快代理SDK，
提供了完整的快代理API集成、代理验证和池管理功能。

主要功能：
- 快代理API集成
- 代理获取和验证
- 代理池管理
- 连接测试和监控
- 配置管理

使用示例：
    from kuaidaili_sdk import KuaidailiClient
    
    # 创建客户端
    client = KuaidailiClient(
        secret_id="your_secret_id",
        signature="your_signature"
    )
    
    # 获取单个代理
    proxy = client.get_proxy()
    
    # 获取代理池
    proxy_pool = client.get_proxy_pool(size=10)
    
    # 验证代理
    is_valid = client.validate_proxy(proxy)

作者: 提取自中国社会组织监控工具项目
版本: 1.0.0
"""

from .client import KuaidailiClient
from .proxy_pool import ProxyPool
from .validator import ProxyValidator
from .config import KuaidailiConfig
from .exceptions import (
    KuaidailiException,
    APIException,
    ProxyException,
    ValidationException
)

# UI组件（可选导入，需要PyQt5）
try:
    from .ui import ProxyDialog, ProxyWidget, ConfigWidget
    _UI_AVAILABLE = True
except ImportError:
    _UI_AVAILABLE = False
    ProxyDialog = None
    ProxyWidget = None
    ConfigWidget = None

__version__ = "1.0.0"
__author__ = "Extracted from Social Organization Monitor Project"

__all__ = [
    'KuaidailiClient',
    'ProxyPool',
    'ProxyValidator',
    'KuaidailiConfig',
    'KuaidailiException',
    'APIException',
    'ProxyException',
    'ValidationException'
]

# 如果UI可用，添加到导出列表
if _UI_AVAILABLE:
    __all__.extend(['ProxyDialog', 'ProxyWidget', 'ConfigWidget'])
