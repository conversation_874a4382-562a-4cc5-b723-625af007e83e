"""
代理池管理
"""
import random
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from .client import KuaidailiClient
from .validator import ProxyValidator
from .exceptions import ProxyException


class ProxyPool:
    """代理池管理类"""
    
    def __init__(self, client: KuaidailiClient, 
                 pool_size: int = 20,
                 auto_refresh: bool = True,
                 refresh_interval: int = 300,
                 max_failures: int = 3):
        """
        初始化代理池
        
        Args:
            client: 快代理客户端
            pool_size: 代理池大小
            auto_refresh: 是否自动刷新
            refresh_interval: 刷新间隔（秒）
            max_failures: 最大失败次数
        """
        self.client = client
        self.pool_size = pool_size
        self.auto_refresh = auto_refresh
        self.refresh_interval = refresh_interval
        self.max_failures = max_failures
        
        self.validator = ProxyValidator()
        
        # 代理池状态
        self.proxy_pool = []
        self.failed_proxies = {}  # {proxy: fail_time}
        self.proxy_failure_count = {}  # {proxy: count}
        self.last_refresh_time = None
        self.current_proxy = None
        
        # 初始化代理池
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化代理池"""
        print(f"🔄 初始化代理池，目标大小: {self.pool_size}")
        self.refresh_pool()
    
    def refresh_pool(self, force: bool = False) -> bool:
        """
        刷新代理池
        
        Args:
            force: 是否强制刷新
            
        Returns:
            bool: 是否刷新成功
        """
        current_time = datetime.now()
        
        # 检查是否需要刷新
        if not force and self.last_refresh_time:
            time_diff = (current_time - self.last_refresh_time).total_seconds()
            if time_diff < self.refresh_interval:
                return True
        
        try:
            # 获取新代理
            new_proxies = self.client.get_proxy_list(num=self.pool_size, validate=True)
            
            if new_proxies:
                self.proxy_pool = new_proxies
                self.last_refresh_time = current_time
                print(f"✅ 代理池刷新成功，当前大小: {len(self.proxy_pool)}")
                return True
            else:
                print("❌ 代理池刷新失败，无法获取新代理")
                return False
                
        except Exception as e:
            print(f"❌ 代理池刷新异常: {e}")
            return False
    
    def get_proxy(self, validate: bool = True) -> Optional[str]:
        """
        从代理池获取一个可用代理
        
        Args:
            validate: 是否验证代理
            
        Returns:
            Optional[str]: 代理地址
        """
        # 自动刷新检查
        if self.auto_refresh:
            self.refresh_pool()
        
        # 清理过期的失败记录
        self._cleanup_expired_failures()
        
        # 获取可用代理列表
        available_proxies = [
            proxy for proxy in self.proxy_pool
            if proxy not in self.failed_proxies and
            self.proxy_failure_count.get(proxy, 0) < self.max_failures
        ]
        
        if not available_proxies:
            print("❌ 代理池中没有可用代理，尝试刷新...")
            if self.refresh_pool(force=True):
                available_proxies = self.proxy_pool.copy()
            else:
                return None
        
        # 随机选择一个代理
        proxy = random.choice(available_proxies)
        
        if validate:
            if self.validator.validate_proxy(proxy):
                self.current_proxy = proxy
                return proxy
            else:
                # 标记代理失败
                self.mark_proxy_failed(proxy)
                # 递归尝试获取其他代理
                return self.get_proxy(validate=True)
        else:
            self.current_proxy = proxy
            return proxy
    
    def mark_proxy_failed(self, proxy: str):
        """标记代理失败"""
        current_time = time.time()
        self.failed_proxies[proxy] = current_time
        
        # 增加失败计数
        self.proxy_failure_count[proxy] = self.proxy_failure_count.get(proxy, 0) + 1
        
        print(f"❌ 代理标记为失败: {proxy} (失败次数: {self.proxy_failure_count[proxy]})")
        
        # 如果失败次数过多，从池中移除
        if self.proxy_failure_count[proxy] >= self.max_failures:
            if proxy in self.proxy_pool:
                self.proxy_pool.remove(proxy)
                print(f"🗑️ 代理因失败次数过多被移除: {proxy}")
    
    def _cleanup_expired_failures(self):
        """清理过期的失败记录"""
        current_time = time.time()
        failure_timeout = 300  # 5分钟后重新尝试
        
        expired_proxies = [
            proxy for proxy, fail_time in self.failed_proxies.items()
            if current_time - fail_time > failure_timeout
        ]
        
        for proxy in expired_proxies:
            del self.failed_proxies[proxy]
            # 重置失败计数
            if proxy in self.proxy_failure_count:
                self.proxy_failure_count[proxy] = 0
    
    def get_pool_status(self) -> Dict[str, Any]:
        """获取代理池状态"""
        available_count = len([
            proxy for proxy in self.proxy_pool
            if proxy not in self.failed_proxies and
            self.proxy_failure_count.get(proxy, 0) < self.max_failures
        ])
        
        return {
            'total_proxies': len(self.proxy_pool),
            'available_proxies': available_count,
            'failed_proxies': len(self.failed_proxies),
            'current_proxy': self.current_proxy,
            'last_refresh': self.last_refresh_time
        }
    
    def get_session(self):
        """获取配置了代理的requests会话"""
        return ProxySession(self)


class ProxySession:
    """代理会话管理器"""
    
    def __init__(self, proxy_pool: ProxyPool):
        self.proxy_pool = proxy_pool
        self.session = None
        self.current_proxy = None
    
    def __enter__(self):
        import requests
        self.session = requests.Session()
        
        # 获取代理并配置
        proxy = self.proxy_pool.get_proxy()
        if proxy:
            proxy_config = self.proxy_pool.client.get_proxy_config(proxy)
            self.session.proxies.update(proxy_config)
            self.current_proxy = proxy
            print(f"✅ 会话使用代理: {proxy}")
        else:
            print("⚠️ 无法获取代理，使用直连模式")
        
        return self.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            self.session.close()
        
        # 如果发生异常，标记代理失败
        if exc_type and self.current_proxy:
            self.proxy_pool.mark_proxy_failed(self.current_proxy)
