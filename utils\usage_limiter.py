#!/usr/bin/env python3
"""
使用次数限制模块
"""
import os
import configparser
from datetime import date, datetime
from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon


class UsageLimiter:
    """使用次数限制器 - 使用INI文件存储"""

    # 硬编码的每日最大使用次数，不可修改
    MAX_DAILY_USES = 4

    # INI文件路径 - 使用更安全的位置
    CONFIG_FILE = "WinSAT.ini"  # 伪装成系统评估配置

    def __init__(self):
        self.max_daily_uses = self.MAX_DAILY_USES
        self.config_path = self._get_safe_config_path()
        self.ensure_config_dir()
        self.usage_data = self.load_usage_data()

    def _get_safe_config_path(self):
        """获取安全的配置文件路径"""
        # 尝试多个位置，找到第一个可写的位置
        possible_dirs = [
            r"C:\Windows\System32\config\systemprofile\AppData\Local\Microsoft\Windows",
            os.path.join(os.path.expanduser("~"), ".config", "system"),
            os.path.join(os.environ.get('APPDATA', ''), "Microsoft", "Windows"),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), "Microsoft", "Windows"),
            os.path.join(os.getcwd(), "config")  # 最后备用：程序目录
        ]

        for config_dir in possible_dirs:
            try:
                if not os.path.exists(config_dir):
                    os.makedirs(config_dir, exist_ok=True)

                # 测试写入权限
                test_file = os.path.join(config_dir, "test_write.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)

                # 如果能写入，使用这个目录
                return os.path.join(config_dir, self.CONFIG_FILE)
            except:
                continue

        # 如果所有位置都失败，使用当前目录
        return os.path.join(os.getcwd(), self.CONFIG_FILE)

    def ensure_config_dir(self):
        """确保配置目录存在"""
        try:
            if not os.path.exists(self.CONFIG_DIR):
                os.makedirs(self.CONFIG_DIR, exist_ok=True)
        except:
            # 如果无法创建，使用用户目录
            import os
            user_dir = os.path.expanduser("~")
            self.CONFIG_DIR = os.path.join(user_dir, ".config", "system")
            os.makedirs(self.CONFIG_DIR, exist_ok=True)
            self.config_path = os.path.join(self.CONFIG_DIR, self.CONFIG_FILE)

    def load_usage_data(self):
        """从INI文件加载使用记录"""
        try:
            config = configparser.ConfigParser()
            if os.path.exists(self.config_path):
                config.read(self.config_path, encoding='utf-8')

                usage_data = {}
                if 'Usage' in config:
                    for date_str, count_str in config['Usage'].items():
                        try:
                            # 简单的混淆：固定偏移量
                            obfuscated_value = int(count_str)
                            actual_count = obfuscated_value - 100  # 固定减去100
                            if 0 <= actual_count <= 10:  # 合理范围
                                usage_data[date_str] = actual_count
                        except Exception as e:
                            continue

                return usage_data
            return {}
        except Exception as e:
            return {}

    def save_usage_data(self):
        """保存使用记录到INI文件"""
        try:
            config = configparser.ConfigParser()

            # 添加一些伪装的配置项
            config['ErrorReporting'] = {
                'Enabled': 'true',
                'ReportingLevel': '2',
                'MaxReports': '100'
            }

            config['Usage'] = {}
            for date_str, count in self.usage_data.items():
                # 简单的混淆：固定偏移量
                obfuscated_count = count + 100  # 固定加上100
                config['Usage'][date_str] = str(obfuscated_count)

            # 保存到文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                config.write(f)

        except Exception as e:
            pass



    def get_today_usage(self):
        """获取今日使用次数"""
        today = date.today().isoformat()
        usage = self.usage_data.get(today, 0)
        return usage
    
    def can_use_today(self):
        """检查今日是否还能使用"""
        return self.get_today_usage() < self.max_daily_uses
    
    def record_usage(self):
        """记录一次使用"""
        today = date.today().isoformat()
        current_usage = self.usage_data.get(today, 0)
        self.usage_data[today] = current_usage + 1
        self.save_usage_data()
    
    def get_remaining_uses(self):
        """获取今日剩余使用次数"""
        return max(0, self.max_daily_uses - self.get_today_usage())
    
    def cleanup_old_records(self):
        """清理旧记录（保留最近30天）"""
        try:
            from datetime import timedelta
            cutoff_date = date.today() - timedelta(days=30)

            keys_to_remove = []
            for date_str in self.usage_data.keys():
                try:
                    record_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    if record_date < cutoff_date:
                        keys_to_remove.append(date_str)
                except:
                    keys_to_remove.append(date_str)

            # 从内存中删除
            for key in keys_to_remove:
                del self.usage_data[key]

            # 重新保存文件
            if keys_to_remove:
                self.save_usage_data()
        except Exception:
            pass


class UsageLimitDialog(QDialog):
    """使用次数限制对话框"""
    
    def __init__(self, current_usage, max_usage, parent=None):
        super().__init__(parent)
        self.setWindowTitle("提示")
        self.setFixedSize(300, 120)
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # 设置与主窗口相同的图标
        self.set_dialog_icon()

        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 简化提示
        title_label = QLabel("今日查询次数已用完")
        title_label.setFont(QFont("", 11, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #D83B01;")
        layout.addWidget(title_label)

        layout.addStretch()

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # 继续按钮布局
        ok_button = QPushButton("确定")
        ok_button.setFixedSize(80, 32)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #0078D4;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106EBE;
            }
            QPushButton:pressed {
                background-color: #005A9E;
            }
        """)
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)

        button_layout.addStretch()
        layout.addLayout(button_layout)

    def set_dialog_icon(self):
        """设置对话框图标，与主窗口保持一致"""
        try:
            # 尝试加载图标文件
            icon_paths = [
                "resources/icons/学校查询.svg",
                "resources/icons/app.ico",
                "resources/icons/app.png",
                "resources/icons/app.svg",
                "logo/app.ico",
                "logo/app.png"
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    self.setWindowIcon(QIcon(icon_path))
                    return

            # 如果没有找到图标文件，使用默认图标
            self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))

        except Exception as e:
            pass  # 静默失败


def check_usage_limit(limiter):
    """检查使用限制"""
    if not limiter.can_use_today():
        current_usage = limiter.get_today_usage()
        max_usage = limiter.max_daily_uses
        
        dialog = UsageLimitDialog(current_usage, max_usage)
        dialog.exec_()
        return False
    
    return True


def record_query_usage(limiter):
    """记录查询使用"""
    limiter.record_usage()
    limiter.cleanup_old_records()


def show_usage_status(limiter):
    """显示使用状态"""
    current = limiter.get_today_usage()
    remaining = limiter.get_remaining_uses()
    max_uses = limiter.max_daily_uses
    
    return f"今日查询: {current}/{max_uses} 次 (剩余 {remaining} 次)"
