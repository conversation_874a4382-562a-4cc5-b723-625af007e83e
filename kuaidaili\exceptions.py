"""
快代理SDK异常定义
"""


class KuaidailiException(Exception):
    """快代理SDK基础异常类"""
    pass


class APIException(KuaidailiException):
    """API相关异常"""
    def __init__(self, message, status_code=None, response_text=None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text


class ProxyException(KuaidailiException):
    """代理相关异常"""
    pass


class ValidationException(KuaidailiException):
    """验证相关异常"""
    pass


class ConfigException(KuaidailiException):
    """配置相关异常"""
    pass
