"""
防双开模块
确保程序只能运行一个实例
"""
import os
import sys
import time
import psutil
import tempfile
from typing import Optional


class SingletonApp:
    """单例应用程序管理器"""
    
    def __init__(self, app_name: str = "GuangdongCollector"):
        self.app_name = app_name
        self.lock_file = None
        self.lock_file_path = None
        self.pid = os.getpid()
        
        # 创建锁文件路径
        temp_dir = tempfile.gettempdir()
        self.lock_file_path = os.path.join(temp_dir, f"{app_name}.lock")
    
    def is_already_running(self) -> bool:
        """检查程序是否已经在运行"""
        try:
            # 检查锁文件是否存在
            if os.path.exists(self.lock_file_path):
                # 读取锁文件中的PID
                with open(self.lock_file_path, 'r') as f:
                    stored_pid = int(f.read().strip())
                
                # 检查该PID的进程是否还在运行
                if self._is_process_running(stored_pid):
                    return True
                else:
                    # 进程已不存在，删除锁文件
                    os.remove(self.lock_file_path)
                    return False
            
            return False
            
        except (ValueError, FileNotFoundError, PermissionError):
            # 锁文件损坏或无法访问，假设没有其他实例
            return False
    
    def _is_process_running(self, pid: int) -> bool:
        """检查指定PID的进程是否在运行"""
        try:
            # 使用psutil检查进程
            if psutil.pid_exists(pid):
                process = psutil.Process(pid)
                # 检查进程名是否匹配（可选）
                return process.is_running()
            return False
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False
    
    def create_lock(self) -> bool:
        """创建锁文件"""
        try:
            # 写入当前进程PID到锁文件
            with open(self.lock_file_path, 'w') as f:
                f.write(str(self.pid))
            
            self.lock_file = self.lock_file_path
            return True
            
        except (PermissionError, OSError) as e:
            print(f"创建锁文件失败: {e}")
            return False
    
    def release_lock(self):
        """释放锁文件"""
        try:
            if self.lock_file and os.path.exists(self.lock_file):
                os.remove(self.lock_file)
                self.lock_file = None
        except (PermissionError, OSError) as e:
            print(f"释放锁文件失败: {e}")
    
    def acquire_lock(self) -> bool:
        """获取应用程序锁"""
        if self.is_already_running():
            return False
        
        return self.create_lock()
    
    def get_running_instance_info(self) -> Optional[dict]:
        """获取正在运行的实例信息"""
        try:
            if os.path.exists(self.lock_file_path):
                with open(self.lock_file_path, 'r') as f:
                    stored_pid = int(f.read().strip())
                
                if self._is_process_running(stored_pid):
                    process = psutil.Process(stored_pid)
                    return {
                        'pid': stored_pid,
                        'name': process.name(),
                        'create_time': process.create_time(),
                        'memory_info': process.memory_info(),
                        'cpu_percent': process.cpu_percent()
                    }
            
            return None
            
        except Exception:
            return None


class SingletonMixin:
    """单例混入类，可以被其他类继承"""
    
    _instances = {}
    _singleton_app = None
    
    def __new__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__new__(cls)
            
            # 初始化单例应用管理器
            if cls._singleton_app is None:
                cls._singleton_app = SingletonApp(cls.__name__)
        
        return cls._instances[cls]
    
    def ensure_single_instance(self) -> bool:
        """确保只有一个实例在运行"""
        if not self._singleton_app.acquire_lock():
            return False
        
        # 注册退出时的清理函数
        import atexit
        atexit.register(self._cleanup_on_exit)
        
        return True
    
    def _cleanup_on_exit(self):
        """退出时清理"""
        if self._singleton_app:
            self._singleton_app.release_lock()


def check_single_instance(app_name: str = "GuangdongCollector") -> tuple[bool, Optional[dict]]:
    """
    检查单实例运行
    返回: (是否可以运行, 已运行实例信息)
    """
    singleton = SingletonApp(app_name)
    
    if singleton.is_already_running():
        instance_info = singleton.get_running_instance_info()
        return False, instance_info
    
    # 创建锁
    if singleton.acquire_lock():
        # 注册退出清理
        import atexit
        atexit.register(singleton.release_lock)
        return True, None
    
    return False, None


def show_already_running_dialog():
    """显示程序已运行的对话框"""
    try:
        # 尝试使用PyQt5显示对话框
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        msg_box = QMessageBox()
        msg_box.setWindowTitle("程序已运行")
        msg_box.setText("广东采集程序已经在运行中！")
        msg_box.setInformativeText("请检查系统托盘或任务栏，程序可能已最小化。")
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.setDefaultButton(QMessageBox.Ok)
        
        # 设置窗口置顶
        msg_box.setWindowFlags(msg_box.windowFlags() | Qt.WindowStaysOnTopHint)
        
        msg_box.exec_()
        
    except ImportError:
        # 如果PyQt5不可用，使用系统消息框
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            
            messagebox.showwarning(
                "程序已运行",
                "广东采集程序已经在运行中！\n请检查系统托盘或任务栏，程序可能已最小化。"
            )
            
            root.destroy()
            
        except ImportError:
            # 最后的备选方案，控制台输出
            print("错误：程序已经在运行中！")
            print("请检查系统托盘或任务栏，程序可能已最小化。")


def main_with_singleton_check(main_func, app_name: str = "GuangdongCollector"):
    """
    带单例检查的主函数装饰器
    """
    def wrapper(*args, **kwargs):
        can_run, instance_info = check_single_instance(app_name)
        
        if not can_run:
            show_already_running_dialog()
            if instance_info:
                print(f"已运行实例PID: {instance_info.get('pid')}")
                print(f"启动时间: {time.ctime(instance_info.get('create_time', 0))}")
            sys.exit(1)
        
        # 运行主函数
        return main_func(*args, **kwargs)
    
    return wrapper


# 使用示例装饰器
def singleton_main(app_name: str = "GuangdongCollector"):
    """单例主函数装饰器"""
    def decorator(func):
        return main_with_singleton_check(func, app_name)
    return decorator
