"""
快代理SDK UI演示应用
展示如何使用UI组件
"""

import sys
import os

# 添加SDK路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QPushButton, QTextEdit,
                             QLabel, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from proxy_dialog import ProxyDialog
from proxy_widget import ProxyWidget
from config_widget import ConfigWidget


class DemoMainWindow(QMainWindow):
    """演示主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("快代理SDK UI演示")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("快代理SDK UI组件演示")
        title_label.setFont(QFont("Microsoft YaHei UI", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #007bff;
                padding: 10px;
                border-bottom: 2px solid #007bff;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 对话框演示选项卡
        dialog_tab = self.create_dialog_tab()
        tab_widget.addTab(dialog_tab, "对话框演示")
        
        # 组件演示选项卡
        widget_tab = self.create_widget_tab()
        tab_widget.addTab(widget_tab, "组件演示")
        
        # 简化组件演示选项卡
        config_tab = self.create_config_tab()
        tab_widget.addTab(config_tab, "简化组件")
        
        main_layout.addWidget(tab_widget)
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_dialog_tab(self):
        """创建对话框演示选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明
        info_label = QLabel("""
        <h3>对话框演示</h3>
        <p>ProxyDialog 是一个独立的对话框，可以在任何应用中使用。</p>
        <p>点击下面的按钮打开代理配置对话框。</p>
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 按钮
        btn_open_dialog = QPushButton("打开代理配置对话框")
        btn_open_dialog.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        btn_open_dialog.clicked.connect(self.open_proxy_dialog)
        layout.addWidget(btn_open_dialog)
        
        # 结果显示
        self.dialog_result = QTextEdit()
        self.dialog_result.setReadOnly(True)
        self.dialog_result.setMaximumHeight(150)
        self.dialog_result.setPlaceholderText("对话框结果将显示在这里...")
        layout.addWidget(self.dialog_result)
        
        layout.addStretch()
        return widget
    
    def create_widget_tab(self):
        """创建组件演示选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明
        info_label = QLabel("""
        <h3>嵌入式组件演示</h3>
        <p>ProxyWidget 可以嵌入到任何窗口中使用，提供完整的代理配置功能。</p>
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 嵌入代理组件
        self.proxy_widget = ProxyWidget()
        self.proxy_widget.config_changed.connect(self.on_widget_config_changed)
        self.proxy_widget.status_changed.connect(self.on_widget_status_changed)
        layout.addWidget(self.proxy_widget)
        
        return widget
    
    def create_config_tab(self):
        """创建简化组件演示选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明
        info_label = QLabel("""
        <h3>简化配置组件演示</h3>
        <p>ConfigWidget 是一个轻量级的配置组件，只包含基本功能。</p>
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 嵌入简化组件
        self.config_widget = ConfigWidget()
        self.config_widget.config_changed.connect(self.on_config_changed)
        self.config_widget.test_completed.connect(self.on_test_completed)
        layout.addWidget(self.config_widget)
        
        # 结果显示
        self.config_result = QTextEdit()
        self.config_result.setReadOnly(True)
        self.config_result.setMaximumHeight(100)
        self.config_result.setPlaceholderText("配置结果将显示在这里...")
        layout.addWidget(self.config_result)
        
        layout.addStretch()
        return widget
    
    def open_proxy_dialog(self):
        """打开代理配置对话框"""
        dialog = ProxyDialog(self)
        
        if dialog.exec_() == ProxyDialog.Accepted:
            config = dialog.get_config()
            if config.is_valid():
                result = f"配置已保存:\nSecret ID: {config.secret_id}\nSignature: {config.signature}"
                self.dialog_result.setPlainText(result)
                self.statusBar().showMessage("对话框配置已保存")
            else:
                self.dialog_result.setPlainText("代理已禁用")
                self.statusBar().showMessage("代理已禁用")
        else:
            self.dialog_result.setPlainText("用户取消了配置")
            self.statusBar().showMessage("用户取消了配置")
    
    def on_widget_config_changed(self, config):
        """组件配置变化处理"""
        if config.is_valid():
            self.statusBar().showMessage(f"组件配置已更新: {config.secret_id[:10]}...")
        else:
            self.statusBar().showMessage("组件配置已清空")
    
    def on_widget_status_changed(self, status):
        """组件状态变化处理"""
        self.statusBar().showMessage(f"组件状态: {status}")
    
    def on_config_changed(self, config):
        """简化组件配置变化处理"""
        if config.is_valid():
            result = f"配置已更新:\nSecret ID: {config.secret_id}\nSignature: {config.signature}"
            self.config_result.setPlainText(result)
        else:
            self.config_result.setPlainText("配置已清空")
    
    def on_test_completed(self, success, message):
        """测试完成处理"""
        if success:
            QMessageBox.information(self, "测试成功", f"API连接成功！\n{message}")
        else:
            QMessageBox.warning(self, "测试失败", f"API连接失败！\n{message}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #ffffff;
        }
        QTabWidget::pane {
            border: 1px solid #dee2e6;
            background-color: #ffffff;
        }
        QTabBar::tab {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #ffffff;
            border-bottom: 1px solid #ffffff;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
        }
    """)
    
    # 创建主窗口
    window = DemoMainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
