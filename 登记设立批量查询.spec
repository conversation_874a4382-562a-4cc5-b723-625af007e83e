# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['E:\\开发项目\\待开发项目\\abc\\main.py'],
    pathex=[],
    binaries=[],
    datas=[('resources', 'resources')],
    hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'requests', 'json', 'configparser', 'utils', 'utils.singleton', 'utils.logger', 'config', 'config.settings', 'gui', 'gui.main_window', 'gui.styles', 'gui.config_dialog', 'core', 'core.query_engine', 'core.proxy_manager', 'core.data_manager', 'core.region_data', 'core.wechat_notifier', 'kuaidaili', 'kuaidaili.client', 'kuaidaili.config'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='登记设立批量查询',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['E:\\开发项目\\待开发项目\\abc\\resources\\icons\\app.ico'],
)
