"""
配置对话框模块
提供用户配置界面
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QLineEdit, QPushButton, QCheckBox,
                            QSpinBox, QGroupBox, QFormLayout, QTextEdit,
                            QMessageBox, QFileDialog, QComboBox, QTimeEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTime
from PyQt5.QtGui import QFont

from config.settings import get_settings
from gui.styles import get_complete_stylesheet, COLORS


class ConfigDialog(QDialog):
    """配置对话框"""
    
    config_changed = pyqtSignal()  # 配置更改信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings = get_settings()
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("配置设置")
        self.setModal(True)
        self.resize(600, 500)
        
        # 应用样式
        self.setStyleSheet(get_complete_stylesheet())
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建各个配置页面
        self.create_general_tab()
        self.create_query_tab()
        self.create_proxy_tab()
        self.create_wechat_tab()
        self.create_ui_tab()
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 测试按钮
        self.test_btn = QPushButton("测试连接")
        self.test_btn.setProperty("class", "secondary")
        self.test_btn.clicked.connect(self.test_connections)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        # 重置按钮
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setProperty("class", "secondary")
        self.reset_btn.clicked.connect(self.reset_config)
        button_layout.addWidget(self.reset_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setProperty("class", "secondary")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        # 确定按钮
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.save_and_close)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
    
    def create_general_tab(self):
        """创建常规设置页面"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 应用程序设置
        app_group = QGroupBox("应用程序设置")
        app_layout = QFormLayout(app_group)
        
        self.max_daily_runs_spin = QSpinBox()
        self.max_daily_runs_spin.setRange(1, 20)
        app_layout.addRow("每日最大运行次数:", self.max_daily_runs_spin)
        
        self.minimize_to_tray_cb = QCheckBox("关闭时最小化到托盘")
        app_layout.addRow(self.minimize_to_tray_cb)
        
        self.auto_start_query_cb = QCheckBox("启动时自动开始查询")
        app_layout.addRow(self.auto_start_query_cb)

        self.scheduled_run_enabled_cb = QCheckBox("启用定时运行")
        app_layout.addRow(self.scheduled_run_enabled_cb)

        self.scheduled_run_time_edit = QTimeEdit()
        self.scheduled_run_time_edit.setDisplayFormat("HH:mm")
        self.scheduled_run_time_edit.setTime(QTime(6, 0))  # 默认6:00
        app_layout.addRow("定时运行时间:", self.scheduled_run_time_edit)

        layout.addWidget(app_group)
        
        # 文件设置
        file_group = QGroupBox("文件设置")
        file_layout = QFormLayout(file_group)
        
        # 地区文件选择
        region_file_layout = QHBoxLayout()
        self.region_file_edit = QLineEdit()
        self.region_file_btn = QPushButton("浏览...")
        self.region_file_btn.setProperty("class", "secondary")
        self.region_file_btn.clicked.connect(self.browse_region_file)
        region_file_layout.addWidget(self.region_file_edit)
        region_file_layout.addWidget(self.region_file_btn)
        file_layout.addRow("地区代码文件:", region_file_layout)
        
        # 输出文件
        output_file_layout = QHBoxLayout()
        self.output_file_edit = QLineEdit()
        self.output_file_btn = QPushButton("浏览...")
        self.output_file_btn.setProperty("class", "secondary")
        self.output_file_btn.clicked.connect(self.browse_output_file)
        output_file_layout.addWidget(self.output_file_edit)
        output_file_layout.addWidget(self.output_file_btn)
        file_layout.addRow("输出数据文件:", output_file_layout)
        
        layout.addWidget(file_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "常规")
    
    def create_query_tab(self):
        """创建查询设置页面"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 查询参数
        query_group = QGroupBox("查询参数")
        query_layout = QFormLayout(query_group)
        
        self.max_retries_spin = QSpinBox()
        self.max_retries_spin.setRange(1, 10)
        query_layout.addRow("最大重试次数:", self.max_retries_spin)
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 60)
        self.timeout_spin.setSuffix(" 秒")
        query_layout.addRow("请求超时时间:", self.timeout_spin)
        
        layout.addWidget(query_group)
        
        # 延迟设置
        delay_group = QGroupBox("延迟设置")
        delay_layout = QFormLayout(delay_group)
        
        self.retry_delay_min_spin = QSpinBox()
        self.retry_delay_min_spin.setRange(1, 30)
        self.retry_delay_min_spin.setSuffix(" 秒")
        delay_layout.addRow("重试延迟最小值:", self.retry_delay_min_spin)
        
        self.retry_delay_max_spin = QSpinBox()
        self.retry_delay_max_spin.setRange(1, 60)
        self.retry_delay_max_spin.setSuffix(" 秒")
        delay_layout.addRow("重试延迟最大值:", self.retry_delay_max_spin)
        
        self.query_delay_min_spin = QSpinBox()
        self.query_delay_min_spin.setRange(1, 10)
        self.query_delay_min_spin.setSuffix(" 秒")
        delay_layout.addRow("查询延迟最小值:", self.query_delay_min_spin)
        
        self.query_delay_max_spin = QSpinBox()
        self.query_delay_max_spin.setRange(1, 20)
        self.query_delay_max_spin.setSuffix(" 秒")
        delay_layout.addRow("查询延迟最大值:", self.query_delay_max_spin)
        
        layout.addWidget(delay_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "查询")
    
    def create_proxy_tab(self):
        """创建代理设置页面"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 代理开关
        self.proxy_enabled_cb = QCheckBox("启用代理")
        layout.addWidget(self.proxy_enabled_cb)
        
        # 快代理API设置
        api_group = QGroupBox("快代理API设置")
        api_layout = QFormLayout(api_group)
        
        self.proxy_username_edit = QLineEdit()
        api_layout.addRow("用户名:", self.proxy_username_edit)
        
        self.proxy_password_edit = QLineEdit()
        self.proxy_password_edit.setEchoMode(QLineEdit.Password)
        api_layout.addRow("密码:", self.proxy_password_edit)
        
        layout.addWidget(api_group)
        
        # 代理切换设置
        switch_group = QGroupBox("代理切换设置")
        switch_layout = QFormLayout(switch_group)
        
        self.proxy_switch_min_spin = QSpinBox()
        self.proxy_switch_min_spin.setRange(1, 50)
        switch_layout.addRow("切换间隔最小值:", self.proxy_switch_min_spin)
        
        self.proxy_switch_max_spin = QSpinBox()
        self.proxy_switch_max_spin.setRange(1, 100)
        switch_layout.addRow("切换间隔最大值:", self.proxy_switch_max_spin)
        
        layout.addWidget(switch_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "代理")
    
    def create_wechat_tab(self):
        """创建企业微信设置页面"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 微信开关
        self.wechat_enabled_cb = QCheckBox("启用企业微信推送")
        layout.addWidget(self.wechat_enabled_cb)
        
        # Webhook设置
        webhook_group = QGroupBox("Webhook设置")
        webhook_layout = QFormLayout(webhook_group)
        
        self.webhook_url_edit = QLineEdit()
        self.webhook_url_edit.setPlaceholderText("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=...")
        webhook_layout.addRow("Webhook URL:", self.webhook_url_edit)
        
        layout.addWidget(webhook_group)
        
        # 推送选项
        options_group = QGroupBox("推送选项")
        options_layout = QVBoxLayout(options_group)
        
        self.send_summary_cb = QCheckBox("发送汇总报告")
        options_layout.addWidget(self.send_summary_cb)
        
        self.send_errors_cb = QCheckBox("发送错误通知")
        options_layout.addWidget(self.send_errors_cb)
        
        layout.addWidget(options_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "微信推送")
    
    def create_ui_tab(self):
        """创建界面设置页面"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 界面设置
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout(ui_group)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 16)
        ui_layout.addRow("字体大小:", self.font_size_spin)
        
        self.log_max_lines_spin = QSpinBox()
        self.log_max_lines_spin.setRange(100, 5000)
        self.log_max_lines_spin.setSuffix(" 行")
        ui_layout.addRow("日志最大行数:", self.log_max_lines_spin)
        
        self.auto_scroll_log_cb = QCheckBox("自动滚动日志")
        ui_layout.addRow(self.auto_scroll_log_cb)
        
        layout.addWidget(ui_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "界面")
    
    def load_config(self):
        """加载配置到界面"""
        # 常规设置
        self.max_daily_runs_spin.setValue(self.settings.get('app.max_daily_runs', 4))
        self.minimize_to_tray_cb.setChecked(self.settings.get('app.minimize_to_tray', True))
        self.auto_start_query_cb.setChecked(self.settings.get('app.auto_start_query', False))
        self.scheduled_run_enabled_cb.setChecked(self.settings.get('app.scheduled_run_enabled', False))

        # 加载定时运行时间
        time_str = self.settings.get('app.scheduled_run_time', '06:00')
        time_obj = QTime.fromString(time_str, 'HH:mm')
        if time_obj.isValid():
            self.scheduled_run_time_edit.setTime(time_obj)
        else:
            self.scheduled_run_time_edit.setTime(QTime(6, 0))
        self.region_file_edit.setText(self.settings.get('query.region_file', 'region_code.json'))
        self.output_file_edit.setText(self.settings.get('query.output_file', 'extracted_data.json'))
        
        # 查询设置
        self.max_retries_spin.setValue(self.settings.get('query.max_retries', 3))
        self.timeout_spin.setValue(self.settings.get('query.timeout', 10))
        self.retry_delay_min_spin.setValue(self.settings.get('query.retry_delay_min', 3))
        self.retry_delay_max_spin.setValue(self.settings.get('query.retry_delay_max', 6))
        self.query_delay_min_spin.setValue(self.settings.get('query.query_delay_min', 1))
        self.query_delay_max_spin.setValue(self.settings.get('query.query_delay_max', 3))
        
        # 代理设置
        self.proxy_enabled_cb.setChecked(self.settings.get('proxy.enabled', False))
        self.proxy_username_edit.setText(self.settings.get('proxy.api_username', ''))
        self.proxy_password_edit.setText(self.settings.get('proxy.api_password', ''))
        self.proxy_switch_min_spin.setValue(self.settings.get('query.proxy_switch_interval_min', 8))
        self.proxy_switch_max_spin.setValue(self.settings.get('query.proxy_switch_interval_max', 12))
        
        # 微信设置
        self.wechat_enabled_cb.setChecked(self.settings.get('wechat.enabled', False))
        self.webhook_url_edit.setText(self.settings.get('wechat.webhook_url', ''))
        self.send_summary_cb.setChecked(self.settings.get('wechat.send_summary', True))
        self.send_errors_cb.setChecked(self.settings.get('wechat.send_errors', True))
        
        # 界面设置
        self.font_size_spin.setValue(self.settings.get('ui.font_size', 9))
        self.log_max_lines_spin.setValue(self.settings.get('ui.log_max_lines', 1000))
        self.auto_scroll_log_cb.setChecked(self.settings.get('ui.auto_scroll_log', True))
    
    def save_config(self):
        """保存配置"""
        # 常规设置
        self.settings.set('app.max_daily_runs', self.max_daily_runs_spin.value())
        self.settings.set('app.minimize_to_tray', self.minimize_to_tray_cb.isChecked())
        self.settings.set('app.auto_start_query', self.auto_start_query_cb.isChecked())
        self.settings.set('app.scheduled_run_enabled', self.scheduled_run_enabled_cb.isChecked())
        self.settings.set('app.scheduled_run_time', self.scheduled_run_time_edit.time().toString('HH:mm'))
        self.settings.set('query.region_file', self.region_file_edit.text())
        self.settings.set('query.output_file', self.output_file_edit.text())
        
        # 查询设置
        self.settings.set('query.max_retries', self.max_retries_spin.value())
        self.settings.set('query.timeout', self.timeout_spin.value())
        self.settings.set('query.retry_delay_min', self.retry_delay_min_spin.value())
        self.settings.set('query.retry_delay_max', self.retry_delay_max_spin.value())
        self.settings.set('query.query_delay_min', self.query_delay_min_spin.value())
        self.settings.set('query.query_delay_max', self.query_delay_max_spin.value())
        
        # 代理设置
        self.settings.set('proxy.enabled', self.proxy_enabled_cb.isChecked())
        self.settings.set('proxy.api_username', self.proxy_username_edit.text())
        self.settings.set('proxy.api_password', self.proxy_password_edit.text())
        self.settings.set('query.proxy_switch_interval_min', self.proxy_switch_min_spin.value())
        self.settings.set('query.proxy_switch_interval_max', self.proxy_switch_max_spin.value())
        
        # 微信设置
        self.settings.set('wechat.enabled', self.wechat_enabled_cb.isChecked())
        self.settings.set('wechat.webhook_url', self.webhook_url_edit.text())
        self.settings.set('wechat.send_summary', self.send_summary_cb.isChecked())
        self.settings.set('wechat.send_errors', self.send_errors_cb.isChecked())
        
        # 界面设置
        self.settings.set('ui.font_size', self.font_size_spin.value())
        self.settings.set('ui.log_max_lines', self.log_max_lines_spin.value())
        self.settings.set('ui.auto_scroll_log', self.auto_scroll_log_cb.isChecked())
        
        # 保存到文件
        return self.settings.save_config()
    
    def browse_region_file(self):
        """浏览地区文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择地区代码文件", "", "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            self.region_file_edit.setText(file_path)
    
    def browse_output_file(self):
        """浏览输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择输出文件", "", "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            self.output_file_edit.setText(file_path)
    
    def test_connections(self):
        """测试连接"""
        # 这里可以添加测试代理和微信webhook的代码
        QMessageBox.information(self, "测试", "连接测试功能待实现")
    
    def reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(
            self, "重置配置", 
            "确定要重置所有配置到默认值吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.settings.reset_to_default()
            self.load_config()
    
    def save_and_close(self):
        """保存并关闭"""
        if self.save_config():
            self.config_changed.emit()
            self.accept()
        else:
            QMessageBox.warning(self, "错误", "保存配置失败！")
