"""
简化的快代理配置组件
只包含基本的配置功能，适合嵌入到其他界面中
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QCheckBox, QMessageBox,
                             QGroupBox, QLineEdit, QFormLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from ..client import KuaidailiClient
from ..config import KuaidailiConfig
from ..exceptions import ConfigException


class ConfigWidget(QWidget):
    """简化的快代理配置组件"""
    
    # 信号定义
    config_changed = pyqtSignal(object)  # 配置变化信号
    test_completed = pyqtSignal(bool, str)  # 测试完成信号

    def __init__(self, parent=None, config=None):
        super().__init__(parent)
        
        # 配置管理
        if config:
            self.config = config
        else:
            self.config = KuaidailiConfig()
        
        self.init_ui()
        self.load_config()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 启用代理复选框
        self.cb_enable_proxy = QCheckBox("启用快代理")
        self.cb_enable_proxy.setFont(QFont("Microsoft YaHei UI", 10, QFont.Bold))
        layout.addWidget(self.cb_enable_proxy)

        # API配置组
        api_group = QGroupBox("API配置")
        api_layout = QFormLayout(api_group)

        # Secret ID
        self.le_secret_id = QLineEdit()
        self.le_secret_id.setPlaceholderText("输入secret_id")
        api_layout.addRow("Secret ID:", self.le_secret_id)

        # Signature
        self.le_signature = QLineEdit()
        self.le_signature.setPlaceholderText("输入signature")
        api_layout.addRow("Signature:", self.le_signature)

        layout.addWidget(api_group)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.btn_test = QPushButton("测试")
        self.btn_save = QPushButton("保存")

        # 按钮样式
        self.btn_test.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        
        self.btn_save.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        button_layout.addWidget(self.btn_test)
        button_layout.addWidget(self.btn_save)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 状态标签
        self.lbl_status = QLabel("状态: 未配置")
        self.lbl_status.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background-color: #f8f9fa;
            }
        """)
        layout.addWidget(self.lbl_status)

        # 连接信号
        self.connect_signals()

    def connect_signals(self):
        """连接信号槽"""
        self.btn_test.clicked.connect(self.test_api)
        self.btn_save.clicked.connect(self.save_config)
        
        # 配置变化时更新状态
        self.cb_enable_proxy.toggled.connect(self.update_status)
        self.le_secret_id.textChanged.connect(self.update_status)
        self.le_signature.textChanged.connect(self.update_status)

    def load_config(self):
        """加载配置"""
        try:
            if self.config.is_valid():
                self.cb_enable_proxy.setChecked(True)
                self.le_secret_id.setText(self.config.secret_id)
                self.le_signature.setText(self.config.signature)
                self.lbl_status.setText("状态: ✅ 已配置")
            else:
                self.cb_enable_proxy.setChecked(False)
                self.lbl_status.setText("状态: ❌ 未配置")
        except ConfigException:
            self.cb_enable_proxy.setChecked(False)
            self.lbl_status.setText("状态: ❌ 未配置")

    def save_config(self):
        """保存配置"""
        enabled = self.cb_enable_proxy.isChecked()
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()

        if enabled and (not secret_id or not signature):
            QMessageBox.warning(
                self, "配置不完整",
                "启用代理但未配置API凭证！"
            )
            return False

        try:
            # 更新配置
            if enabled and secret_id and signature:
                self.config = KuaidailiConfig(secret_id=secret_id, signature=signature)
                self.lbl_status.setText("状态: ✅ 已保存")
            else:
                self.config = KuaidailiConfig()
                self.lbl_status.setText("状态: ❌ 已禁用")

            # 发送配置变化信号
            self.config_changed.emit(self.config)
            return True

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置时发生错误：{str(e)}")
            return False

    def test_api(self):
        """测试API连接"""
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()
        
        if not secret_id or not signature:
            QMessageBox.warning(self, "输入错误", "请先输入API凭证！")
            return
        
        try:
            # 创建临时客户端进行测试
            temp_config = KuaidailiConfig(secret_id=secret_id, signature=signature)
            temp_client = KuaidailiClient(config=temp_config)
            
            # 禁用按钮，显示测试中
            self.btn_test.setEnabled(False)
            self.btn_test.setText("测试中...")
            self.lbl_status.setText("状态: 🔄 测试中...")
            
            # 测试连接
            success, message = temp_client.test_connection()
            
            # 恢复按钮
            self.btn_test.setEnabled(True)
            self.btn_test.setText("测试")
            
            if success:
                self.lbl_status.setText("状态: ✅ 连接成功")
                self.test_completed.emit(True, message)
            else:
                self.lbl_status.setText("状态: ❌ 连接失败")
                self.test_completed.emit(False, message)
                
        except Exception as e:
            # 恢复按钮
            self.btn_test.setEnabled(True)
            self.btn_test.setText("测试")
            self.lbl_status.setText("状态: ❌ 测试异常")
            self.test_completed.emit(False, str(e))

    def update_status(self):
        """更新状态显示"""
        enabled = self.cb_enable_proxy.isChecked()
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()

        if not enabled:
            self.lbl_status.setText("状态: ❌ 已禁用")
        elif not secret_id or not signature:
            self.lbl_status.setText("状态: ⚠️ 配置不完整")
        else:
            self.lbl_status.setText("状态: ⚠️ 未保存")

    def get_config(self):
        """获取当前配置"""
        return self.config

    def set_config(self, config):
        """设置配置"""
        self.config = config
        self.load_config()

    def is_enabled(self):
        """是否启用代理"""
        return self.cb_enable_proxy.isChecked()

    def is_configured(self):
        """是否已配置"""
        return (self.le_secret_id.text().strip() and 
                self.le_signature.text().strip())
