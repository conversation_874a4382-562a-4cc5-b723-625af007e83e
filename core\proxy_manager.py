"""
代理管理模块
集成快代理API，提供代理池管理功能
"""
import random
import time
from typing import Dict, Optional, List
import sys
import os

# 添加kuaidaili模块路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'kuaidaili'))

try:
    from kuaidaili.client import KuaidailiClient
    from kuaidaili.config import KuaidailiConfig
    KUAIDAILI_AVAILABLE = True
except ImportError as e:
    print(f"快代理模块导入失败: {e}")
    KUAIDAILI_AVAILABLE = False


class ProxyManager:
    """代理管理器"""
    
    def __init__(self, api_config: Dict = None, logger=None):
        self.logger = logger
        self.current_proxy = None
        self.proxy_list = []
        self.proxy_index = 0
        self.switch_count = 0
        self.api_config = api_config or {}
        self.client = None
        
        # 初始化快代理客户端
        if KUAIDAILI_AVAILABLE and self.api_config:
            self._init_kuaidaili_client()
        
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        if self.logger:
            self.logger.log(message, level)
        else:
            print(f"[{level}] {message}")
    
    def _init_kuaidaili_client(self):
        """初始化快代理客户端"""
        try:
            username = self.api_config.get('api_username')
            password = self.api_config.get('api_password')

            self.log(f"🔧 尝试初始化快代理客户端...")
            self.log(f"🔧 快代理模块可用: {KUAIDAILI_AVAILABLE}")

            if not KUAIDAILI_AVAILABLE:
                self.log("❌ 快代理模块不可用", "ERROR")
                self.client = None
                return

            if username and password:
                # 使用快代理客户端的正确初始化方式
                self.client = KuaidailiClient(secret_id=username, signature=password)
                self.log("✅ 快代理客户端初始化成功")
            else:
                self.log("❌ 快代理配置不完整，将使用无代理模式", "WARNING")
                self.client = None
        except Exception as e:
            self.log(f"❌ 快代理客户端初始化失败: {e}", "ERROR")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}", "ERROR")
            self.client = None
    
    def get_proxy_from_api(self, max_retries: int = 3) -> Optional[Dict]:
        """从快代理API获取代理，支持重试"""
        if not self.client:
            return None

        for attempt in range(max_retries):
            try:
                # 获取代理IP（快代理返回的是字符串格式，如 "ip:port"）
                proxy_str = self.client.get_proxy(validate=False)  # 我们自己测试
                if proxy_str:
                    # 转换为字典格式
                    proxy = {
                        'http': f"http://{proxy_str}",
                        'https': f"http://{proxy_str}"
                    }
                    self.log(f"✅ 提取到新代理: {proxy_str}")

                    # 测试代理是否可用
                    if self.test_proxy(proxy):
                        return proxy
                    else:
                        self.log(f"❌ 代理测试失败: {proxy_str}")
                        if attempt < max_retries - 1:
                            self.log(f"🔄 重新提取代理 (第{attempt + 2}次尝试)")
                            continue
                else:
                    self.log(f"❌ API未返回代理IP (第{attempt + 1}次尝试)")

            except Exception as e:
                self.log(f"从API获取代理失败 (第{attempt + 1}次尝试): {e}", "ERROR")
                if attempt < max_retries - 1:
                    continue

        self.log(f"❌ 经过{max_retries}次尝试，无法获取可用代理")
        return None
    
    def add_static_proxies(self, proxy_list: List[Dict]):
        """添加静态代理列表"""
        self.proxy_list.extend(proxy_list)
        self.log(f"添加了 {len(proxy_list)} 个静态代理")
    
    def get_proxy(self, max_api_retries: int = 3) -> Optional[Dict]:
        """获取当前代理"""
        # 如果有快代理API，直接返回当前代理（不重复获取）
        if self.client and self.current_proxy:
            return self.current_proxy

        # 如果没有当前代理，从API获取（多次尝试）
        if self.client:
            self.log("🔍 尝试从API获取代理...")
            api_proxy = self.get_proxy_from_api(max_retries=max_api_retries)
            if api_proxy:
                self.current_proxy = api_proxy
                return self.current_proxy
            else:
                self.log("⚠️ 无法从API获取可用代理，尝试使用静态代理", "WARNING")

        # 使用静态代理列表（备用）
        if self.proxy_list:
            self.log("🔍 尝试使用静态代理列表...")
            if self.proxy_index >= len(self.proxy_list):
                self.proxy_index = 0

            self.current_proxy = self.proxy_list[self.proxy_index]

            # 测试静态代理是否可用
            if self.test_proxy(self.current_proxy):
                self.log(f"✅ 静态代理可用: {self.proxy_index + 1}/{len(self.proxy_list)}")
                return self.current_proxy
            else:
                self.log(f"❌ 静态代理不可用: {self.proxy_index + 1}/{len(self.proxy_list)}")
                # 尝试下一个静态代理
                self.proxy_index += 1
                if self.proxy_index < len(self.proxy_list):
                    return self.get_proxy(max_api_retries=0)  # 递归调用但不再尝试API

        # 无代理模式
        self.log("⚠️ 无可用代理，使用直连模式", "WARNING")
        self.current_proxy = None
        return None
    
    def switch_proxy(self):
        """切换代理"""
        self.switch_count += 1
        self.log(f"🔄 开始切换代理 (第{self.switch_count}次切换)")

        # 如果有API，强制获取新的API代理
        if self.client:
            self.log("📡 从快代理API提取新代理...")
            new_proxy = self.get_proxy_from_api()
            if new_proxy:
                self.current_proxy = new_proxy
                self.log("✅ 已切换到新的API代理")
                return
            else:
                self.log("❌ API代理提取失败", "ERROR")

        # 如果API失败，尝试静态代理
        if self.proxy_list:
            self.proxy_index = (self.proxy_index + 1) % len(self.proxy_list)
            self.current_proxy = self.proxy_list[self.proxy_index]
            self.log(f"🔄 已切换到静态代理 {self.proxy_index + 1}/{len(self.proxy_list)}")
        else:
            self.log("⚠️ 无可用代理，使用直连模式", "WARNING")
            self.current_proxy = None
    
    def test_proxy(self, proxy: Dict, test_url: str = "https://www.baidu.com",
                   timeout: int = 10) -> bool:
        """测试代理是否可用"""
        try:
            import requests
            response = requests.get(test_url, proxies=proxy, timeout=timeout)
            if response.status_code == 200:
                return True
        except Exception as e:
            self.log(f"代理测试失败: {proxy}, 错误: {e}", "WARNING")

        return False
    
    def get_proxy_stats(self) -> Dict:
        """获取代理统计信息"""
        return {
            "current_proxy": self.current_proxy,
            "static_proxy_count": len(self.proxy_list),
            "switch_count": self.switch_count,
            "api_available": self.client is not None,
            "kuaidaili_available": KUAIDAILI_AVAILABLE
        }
    
    def update_config(self, api_config: Dict):
        """更新代理配置"""
        self.api_config = api_config
        if KUAIDAILI_AVAILABLE:
            self._init_kuaidaili_client()
        else:
            self.log("快代理模块不可用", "WARNING")


# 默认代理配置示例（清空静态代理，只使用快代理API）
DEFAULT_STATIC_PROXIES = []


def create_proxy_manager(config: Dict = None, logger=None) -> ProxyManager:
    """创建代理管理器实例"""
    manager = ProxyManager(config, logger)
    
    # 添加默认静态代理
    if DEFAULT_STATIC_PROXIES:
        manager.add_static_proxies(DEFAULT_STATIC_PROXIES)
    
    return manager
