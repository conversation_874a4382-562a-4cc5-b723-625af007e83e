# 快代理SDK UI组件

这个UI模块提供了可复用的快代理配置界面组件，基于原项目的优秀设计，适配快代理SDK。

## 🎯 设计理念

- **可复用**: 组件可以轻松集成到任何PyQt5项目中
- **模块化**: 提供不同复杂度的组件，满足不同需求
- **美观**: 现代化的界面设计，用户体验良好
- **功能完整**: 包含配置、测试、获取代理等完整功能

## 📦 组件列表

### 1. ProxyDialog - 独立对话框
完整的代理配置对话框，可以作为独立窗口使用。

**特性:**
- ✅ 完整的快代理API配置
- ✅ API连接测试
- ✅ 代理获取和显示
- ✅ 配置保存和验证
- ✅ 美观的界面设计

**使用场景:**
- 应用程序的设置对话框
- 独立的代理配置工具
- 插件式的配置界面

### 2. ProxyWidget - 嵌入式组件
可嵌入到其他窗口中的代理配置组件。

**特性:**
- ✅ 完整的配置功能
- ✅ 信号机制通知配置变化
- ✅ 可控制的代理列表显示
- ✅ 实时状态更新

**使用场景:**
- 主窗口的配置区域
- 选项卡中的代理设置
- 复杂界面的一部分

### 3. ConfigWidget - 简化组件
轻量级的配置组件，只包含基本功能。

**特性:**
- ✅ 基本的API配置
- ✅ 连接测试
- ✅ 状态显示
- ✅ 紧凑的界面

**使用场景:**
- 空间有限的界面
- 简单的配置需求
- 快速集成场景

## 🚀 使用示例

### 独立对话框使用

```python
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton
from kuaidaili_sdk.ui import ProxyDialog

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        btn = QPushButton("代理配置", self)
        btn.clicked.connect(self.open_proxy_dialog)
        self.setCentralWidget(btn)
    
    def open_proxy_dialog(self):
        dialog = ProxyDialog(self)
        if dialog.exec_() == ProxyDialog.Accepted:
            config = dialog.get_config()
            print(f"配置已保存: {config.is_valid()}")

app = QApplication([])
window = MainWindow()
window.show()
app.exec_()
```

### 嵌入式组件使用

```python
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from kuaidaili_sdk.ui import ProxyWidget

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        # 嵌入代理组件
        proxy_widget = ProxyWidget()
        proxy_widget.config_changed.connect(self.on_config_changed)
        proxy_widget.status_changed.connect(self.on_status_changed)
        
        layout.addWidget(proxy_widget)
        self.setCentralWidget(central_widget)
    
    def on_config_changed(self, config):
        print(f"配置变化: {config.is_valid()}")
    
    def on_status_changed(self, status):
        print(f"状态变化: {status}")

app = QApplication([])
window = MainWindow()
window.show()
app.exec_()
```

### 简化组件使用

```python
from PyQt5.QtWidgets import QApplication, QMainWindow
from kuaidaili_sdk.ui import ConfigWidget

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        config_widget = ConfigWidget()
        config_widget.config_changed.connect(self.on_config_changed)
        config_widget.test_completed.connect(self.on_test_completed)
        
        self.setCentralWidget(config_widget)
    
    def on_config_changed(self, config):
        print(f"配置更新: {config.to_dict()}")
    
    def on_test_completed(self, success, message):
        print(f"测试结果: {success} - {message}")

app = QApplication([])
window = MainWindow()
window.show()
app.exec_()
```

## 🎨 界面特色

### 现代化设计
- 使用现代化的颜色方案
- 圆角边框和阴影效果
- 清晰的视觉层次

### 用户友好
- 清晰的状态提示
- 实时的配置验证
- 友好的错误信息

### 响应式布局
- 自适应窗口大小
- 合理的组件间距
- 优雅的布局设计

## 🔧 自定义和扩展

### 样式自定义

```python
# 自定义按钮样式
proxy_widget.btn_test_api.setStyleSheet("""
    QPushButton {
        background-color: #28a745;
        color: white;
        border-radius: 6px;
        padding: 8px 16px;
    }
""")

# 隐藏代理列表
proxy_widget.set_proxy_list_visible(False)
```

### 信号连接

```python
# 连接配置变化信号
proxy_widget.config_changed.connect(lambda config: 
    print(f"新配置: {config.to_dict()}"))

# 连接状态变化信号
proxy_widget.status_changed.connect(lambda status:
    self.statusBar().showMessage(status))
```

### 配置预设

```python
from kuaidaili_sdk import KuaidailiConfig

# 预设配置
preset_config = KuaidailiConfig(
    secret_id="your_secret_id",
    signature="your_signature"
)

# 使用预设配置创建组件
proxy_widget = ProxyWidget(config=preset_config)
```

## 🧪 演示应用

运行演示应用查看所有组件的效果：

```bash
cd kuaidaili_sdk/ui
python demo_app.py
```

演示应用包含：
- 对话框演示
- 嵌入式组件演示  
- 简化组件演示

## 📋 依赖要求

- PyQt5 >= 5.15.0
- kuaidaili_sdk (核心SDK)

## 💡 最佳实践

1. **选择合适的组件**: 根据界面复杂度选择对应的组件
2. **处理信号**: 及时处理配置变化和状态变化信号
3. **错误处理**: 做好异常情况的用户提示
4. **样式统一**: 保持与应用整体风格的一致性
5. **用户体验**: 提供清晰的操作反馈

## 🔄 从原项目迁移

如果你正在使用原项目的代理配置界面，可以这样迁移：

### 原代码
```python
from ui.proxy_dialog import ProxyDialog
from utils.config import ConfigManager

config_manager = ConfigManager()
dialog = ProxyDialog(parent)
```

### 新代码
```python
from kuaidaili_sdk.ui import ProxyDialog
from kuaidaili_sdk import KuaidailiConfig

config = KuaidailiConfig.from_config_file("config.ini")
dialog = ProxyDialog(parent, config)
```

## 🎯 总结

这些UI组件完美地保留了原项目代理配置界面的优秀设计，同时适配了新的SDK架构，提供了更好的复用性和扩展性。无论是简单的配置需求还是复杂的界面集成，都能找到合适的组件。
